<?php
require_once 'includes/db_connect.php';

// Create users table
$users_table = "CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'editor') DEFAULT 'editor',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

// Create categories table
$categories_table = "CREATE TABLE IF NOT EXISTS categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

// Create blog_posts table
$posts_table = "CREATE TABLE IF NOT EXISTS blog_posts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    excerpt TEXT,
    content LONGTEXT NOT NULL,
    image_url VARCHAR(500),
    category_id INT,
    author_id INT NOT NULL,
    status ENUM('draft', 'published') DEFAULT 'draft',
    publish_date DATETIME NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_slug (slug),
    INDEX idx_status (status),
    INDEX idx_publish_date (publish_date)
)";

try {
    // Create tables
    $conn->query($users_table);
    echo "Users table created successfully<br>";
    
    $conn->query($categories_table);
    echo "Categories table created successfully<br>";
    
    $conn->query($posts_table);
    echo "Blog posts table created successfully<br>";
    
    // Create default admin user
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    $admin_sql = "INSERT IGNORE INTO users (username, email, password, role) 
                  VALUES ('admin', '<EMAIL>', '$admin_password', 'admin')";
    $conn->query($admin_sql);
    echo "Default admin user created (username: admin, password: admin123)<br>";
    
    // Create default categories
    $default_categories = [
        ['Refrigerator Repair', 'Tips and guides for refrigerator maintenance and repair'],
        ['Washing Machine', 'Washing machine troubleshooting and maintenance'],
        ['Cold Room Services', 'Commercial cold room maintenance and repair'],
        ['Appliance Tips', 'General appliance care and maintenance tips'],
        ['Energy Efficiency', 'Energy-saving tips for home appliances']
    ];
    
    foreach ($default_categories as $category) {
        $name = $conn->real_escape_string($category[0]);
        $description = $conn->real_escape_string($category[1]);
        $category_sql = "INSERT IGNORE INTO categories (name, description) VALUES ('$name', '$description')";
        $conn->query($category_sql);
    }
    echo "Default categories created<br>";
    
    echo "<br><strong>Setup completed successfully!</strong><br>";
    echo "You can now access the admin panel at: <a href='admin/login.php'>admin/login.php</a><br>";
    echo "Default login: admin / admin123<br>";
    echo "<br><em>Remember to change the default password after first login!</em>";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>
