<?php
require_once 'includes/db_connect.php';
require_once 'includes/env_loader.php';

// Set content type to XML
header('Content-Type: application/xml; charset=utf-8');

// Get site URL from environment or use default
$siteUrl = $_ENV['SITE_URL'] ?? 'http://localhost/lisbon-appliances';
$siteUrl = rtrim($siteUrl, '/');

// Start XML output
echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

// Add homepage
echo "  <url>\n";
echo "    <loc>{$siteUrl}/</loc>\n";
echo "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
echo "    <changefreq>daily</changefreq>\n";
echo "    <priority>1.0</priority>\n";
echo "  </url>\n";

// Add blog page
echo "  <url>\n";
echo "    <loc>{$siteUrl}/blog.php</loc>\n";
echo "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
echo "    <changefreq>daily</changefreq>\n";
echo "    <priority>0.8</priority>\n";
echo "  </url>\n";

// Add published blog posts
$posts_sql = "SELECT slug, publish_date, updated_at FROM blog_posts WHERE status = 'published' ORDER BY publish_date DESC";
$posts_result = $conn->query($posts_sql);

if ($posts_result->num_rows > 0) {
    while($post = $posts_result->fetch_assoc()) {
        $lastmod = $post['updated_at'] ? $post['updated_at'] : $post['publish_date'];
        $lastmod = date('Y-m-d\TH:i:s+00:00', strtotime($lastmod));
        
        echo "  <url>\n";
        echo "    <loc>{$siteUrl}/article.php?slug=" . htmlspecialchars($post['slug']) . "</loc>\n";
        echo "    <lastmod>{$lastmod}</lastmod>\n";
        echo "    <changefreq>weekly</changefreq>\n";
        echo "    <priority>0.6</priority>\n";
        echo "  </url>\n";
    }
}

// Add categories
$categories_sql = "SELECT DISTINCT c.slug, c.name FROM categories c 
                   INNER JOIN blog_posts p ON c.id = p.category_id 
                   WHERE p.status = 'published'";
$categories_result = $conn->query($categories_sql);

if ($categories_result->num_rows > 0) {
    while($category = $categories_result->fetch_assoc()) {
        echo "  <url>\n";
        echo "    <loc>{$siteUrl}/blog.php?category=" . urlencode($category['name']) . "</loc>\n";
        echo "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
        echo "    <changefreq>weekly</changefreq>\n";
        echo "    <priority>0.5</priority>\n";
        echo "  </url>\n";
    }
}

echo '</urlset>';

$conn->close();
?>
