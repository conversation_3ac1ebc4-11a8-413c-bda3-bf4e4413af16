<?php
session_start();
require_once '../includes/db_connect.php';
require_once 'includes/auth_check.php';

$success = '';
$error = '';

// Get categories
$categories_sql = "SELECT id, name FROM categories ORDER BY name";
$categories_result = $conn->query($categories_sql);
$categories = [];
if ($categories_result->num_rows > 0) {
    while($row = $categories_result->fetch_assoc()) {
        $categories[] = $row;
    }
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $title = $conn->real_escape_string($_POST['title']);
    $slug = $conn->real_escape_string($_POST['slug']);
    $excerpt = $conn->real_escape_string($_POST['excerpt']);
    $content = $conn->real_escape_string($_POST['content']);
    $category_id = (int)$_POST['category_id'];
    $status = $conn->real_escape_string($_POST['status']);
    $image_url = $conn->real_escape_string($_POST['image_url']);
    $author_id = $_SESSION['admin_id'];
    
    // Generate slug if empty
    if (empty($slug)) {
        $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $title)));
    }
    
    // Check if slug exists
    $slug_check = $conn->query("SELECT id FROM blog_posts WHERE slug = '$slug'");
    if ($slug_check->num_rows > 0) {
        $slug .= '-' . time();
    }
    
    $publish_date = ($status == 'published') ? date('Y-m-d H:i:s') : null;
    
    $sql = "INSERT INTO blog_posts (title, slug, excerpt, content, category_id, author_id, status, image_url, publish_date, created_at, updated_at) 
            VALUES ('$title', '$slug', '$excerpt', '$content', $category_id, $author_id, '$status', '$image_url', " . 
            ($publish_date ? "'$publish_date'" : "NULL") . ", NOW(), NOW())";
    
    if ($conn->query($sql)) {
        $success = 'Post created successfully!';
        if ($status == 'published') {
            $success .= ' <a href="../article.php?slug=' . $slug . '" target="_blank">View Post</a>';
        }
    } else {
        $error = 'Error creating post: ' . $conn->error;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add New Post - Lisbon Appliances Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/admin.css">
    <script src="https://cdn.ckeditor.com/ckeditor5/40.0.0/classic/ckeditor.js"></script>
    <style>
        .ck-editor__editable {
            min-height: 400px;
        }
        .ck.ck-editor {
            max-width: 100%;
        }
        .ck-content {
            font-family: 'Roboto', sans-serif;
        }
        .ai-helper {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
        .ai-panel {
            display: none;
            position: fixed;
            bottom: 80px;
            right: 20px;
            width: 400px;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            z-index: 1001;
            max-height: 600px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Add New Post</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="posts.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to Posts
                        </a>
                    </div>
                </div>

                <?php if ($success): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <form method="POST" id="postForm">
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card mb-4">
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="title" class="form-label">Title</label>
                                        <input type="text" class="form-control" id="title" name="title" required 
                                               onkeyup="generateSlug()" placeholder="Enter post title">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="slug" class="form-label">Slug</label>
                                        <input type="text" class="form-control" id="slug" name="slug" 
                                               placeholder="auto-generated-from-title">
                                        <div class="form-text">URL-friendly version of the title</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="excerpt" class="form-label">Excerpt</label>
                                        <textarea class="form-control" id="excerpt" name="excerpt" rows="3" 
                                                  placeholder="Brief description of the post"></textarea>
                                        <div class="form-text">Brief summary for SEO and previews</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <label for="content" class="form-label mb-0">Content</label>
                                            <button type="button" class="btn btn-sm btn-primary" onclick="toggleAIPanel()">
                                                <i class="fas fa-robot me-1"></i>AI Help
                                            </button>
                                        </div>
                                        <textarea name="content" id="contentEditor"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-4">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">Publish</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="status" class="form-label">Status</label>
                                        <select class="form-select" id="status" name="status">
                                            <option value="draft">Draft</option>
                                            <option value="published">Published</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="category_id" class="form-label">Category</label>
                                        <select class="form-select" id="category_id" name="category_id" required>
                                            <option value="">Select Category</option>
                                            <?php foreach ($categories as $category): ?>
                                                <option value="<?php echo $category['id']; ?>">
                                                    <?php echo htmlspecialchars($category['name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i>Save Post
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" onclick="previewPost()">
                                            <i class="fas fa-eye me-1"></i>Preview
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Featured Image</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">Upload Image</label>
                                        <input type="file" class="form-control" id="imageFile" accept="image/*" onchange="uploadImage()">
                                        <div class="form-text">Or enter image URL below</div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="image_url" class="form-label">Image URL</label>
                                        <input type="url" class="form-control" id="image_url" name="image_url"
                                               placeholder="https://example.com/image.jpg" onchange="previewImage()">
                                    </div>
                                    <div id="uploadProgress" class="progress mb-3" style="display: none;">
                                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                    </div>
                                    <div id="imagePreview" class="text-center" style="display: none;">
                                        <img id="previewImg" src="" alt="Preview" class="img-fluid rounded" style="max-height: 200px;">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </main>
        </div>
    </div>

    <!-- AI Helper Panel -->
    <div class="ai-panel" id="aiPanel">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0"><i class="fas fa-robot me-2"></i>AI Assistant</h6>
                <button type="button" class="btn-close" onclick="toggleAIPanel()"></button>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <select class="form-select form-select-sm" id="aiContentType">
                        <option value="content">Generate Content</option>
                        <option value="title">Generate Title</option>
                        <option value="excerpt">Generate Excerpt</option>
                        <option value="outline">Article Outline</option>
                        <option value="introduction">Introduction</option>
                        <option value="conclusion">Conclusion</option>
                        <option value="transcript_to_article">Transcript to Article</option>
                        <option value="repurpose_article">Repurpose Article</option>
                    </select>
                </div>
                <div class="mb-3">
                    <textarea class="form-control" id="aiPrompt" rows="3" 
                              placeholder="Describe what you want to generate..."></textarea>
                </div>
                <button type="button" class="btn btn-primary btn-sm w-100" onclick="generateAIContent()">
                    <i class="fas fa-magic me-1"></i>Generate
                </button>
                <div id="aiResult" class="mt-3" style="display: none;">
                    <div class="border rounded p-2 bg-light">
                        <div id="aiResultContent"></div>
                        <div class="mt-2">
                            <button type="button" class="btn btn-success btn-sm" onclick="useAIContent()">
                                <i class="fas fa-check me-1"></i>Use This
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="copyAIContent()">
                                <i class="fas fa-copy me-1"></i>Copy
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- AI Helper Button -->
    <div class="ai-helper">
        <button type="button" class="btn btn-primary rounded-circle" onclick="toggleAIPanel()" title="AI Assistant">
            <i class="fas fa-robot"></i>
        </button>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let editor;

        // Initialize CKEditor
        ClassicEditor
            .create(document.querySelector('#contentEditor'), {
                toolbar: {
                    items: [
                        'heading', '|',
                        'bold', 'italic', 'underline', '|',
                        'link', 'bulletedList', 'numberedList', '|',
                        'outdent', 'indent', '|',
                        'imageUpload', 'blockQuote', 'insertTable', '|',
                        'undo', 'redo', '|',
                        'sourceEditing'
                    ]
                },
                heading: {
                    options: [
                        { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
                        { model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' },
                        { model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3' },
                        { model: 'heading4', view: 'h4', title: 'Heading 4', class: 'ck-heading_heading4' }
                    ]
                },
                image: {
                    toolbar: [
                        'imageTextAlternative', 'imageStyle:inline', 'imageStyle:block', 'imageStyle:side'
                    ]
                },
                table: {
                    contentToolbar: [
                        'tableColumn', 'tableRow', 'mergeTableCells'
                    ]
                }
            })
            .then(newEditor => {
                editor = newEditor;
            })
            .catch(error => {
                console.error(error);
            });

        function generateSlug() {
            const title = document.getElementById('title').value;
            const slug = title.toLowerCase()
                .replace(/[^a-z0-9 -]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');
            document.getElementById('slug').value = slug;
        }
        
        function previewImage() {
            const url = document.getElementById('image_url').value;
            const preview = document.getElementById('imagePreview');
            const img = document.getElementById('previewImg');

            if (url) {
                img.src = url;
                preview.style.display = 'block';
            } else {
                preview.style.display = 'none';
            }
        }

        function uploadImage() {
            const fileInput = document.getElementById('imageFile');
            const file = fileInput.files[0];

            if (!file) return;

            // Validate file type
            if (!file.type.startsWith('image/')) {
                alert('Please select an image file');
                fileInput.value = '';
                return;
            }

            // Validate file size (5MB)
            if (file.size > 5 * 1024 * 1024) {
                alert('File size must be less than 5MB');
                fileInput.value = '';
                return;
            }

            const formData = new FormData();
            formData.append('image', file);

            const progressBar = document.getElementById('uploadProgress');
            const progressBarInner = progressBar.querySelector('.progress-bar');

            progressBar.style.display = 'block';
            progressBarInner.style.width = '0%';

            fetch('api/upload_image.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('image_url').value = data.url;
                    previewImage();
                    progressBarInner.style.width = '100%';
                    setTimeout(() => {
                        progressBar.style.display = 'none';
                    }, 1000);
                } else {
                    alert('Upload failed: ' + data.error);
                    progressBar.style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Upload error:', error);
                alert('Upload failed. Please try again.');
                progressBar.style.display = 'none';
            });
        }
        
        function previewPost() {
            const title = document.getElementById('title').value;
            const content = document.getElementById('contentEditor').innerHTML;
            
            if (!title || !content) {
                alert('Please fill in title and content first');
                return;
            }
            
            const previewWindow = window.open('', '_blank');
            previewWindow.document.write(`
                <html>
                <head>
                    <title>${title} - Preview</title>
                    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
                    <style>
                        body { padding: 2rem; }
                        .preview-header { background: #f8f9fa; padding: 1rem; border-radius: 8px; margin-bottom: 2rem; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="preview-header">
                            <h6 class="text-muted mb-0">PREVIEW MODE</h6>
                        </div>
                        <h1>${title}</h1>
                        <div class="content">${content}</div>
                    </div>
                </body>
                </html>
            `);
        }
        
        let currentAIResult = '';
        
        function toggleAIPanel() {
            const panel = document.getElementById('aiPanel');
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        }
        
        function generateAIContent() {
            const prompt = document.getElementById('aiPrompt').value.trim();
            const type = document.getElementById('aiContentType').value;
            
            if (!prompt) {
                alert('Please enter a prompt');
                return;
            }
            
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Generating...';
            button.disabled = true;
            
            fetch('api/ai_content.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    prompt: prompt,
                    type: type
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentAIResult = data.content;
                    document.getElementById('aiResultContent').innerHTML = type === 'content' ? data.content : data.content;
                    document.getElementById('aiResult').style.display = 'block';
                } else {
                    alert('Error: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                alert('Network error. Please try again.');
                console.error('Error:', error);
            })
            .finally(() => {
                button.innerHTML = originalText;
                button.disabled = false;
            });
        }
        
        function useAIContent() {
            const type = document.getElementById('aiContentType').value;

            switch (type) {
                case 'title':
                    document.getElementById('title').value = currentAIResult;
                    generateSlug();
                    break;
                case 'excerpt':
                    document.getElementById('excerpt').value = currentAIResult;
                    break;
                case 'content':
                    if (editor) {
                        editor.setData(currentAIResult);
                    }
                    break;
            }

            toggleAIPanel();
        }
        
        function copyAIContent() {
            navigator.clipboard.writeText(currentAIResult).then(() => {
                const button = event.target;
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check me-1"></i>Copied!';
                setTimeout(() => {
                    button.innerHTML = originalText;
                }, 2000);
            });
        }
        
        // Form submission handler
        document.getElementById('postForm').addEventListener('submit', function(e) {
            updateHiddenContent();
            
            const title = document.getElementById('title').value.trim();
            const content = document.getElementById('hiddenContent').value.trim();
            
            if (!title || !content) {
                e.preventDefault();
                alert('Please fill in all required fields');
                return;
            }
        });
    </script>
</body>
</html>
