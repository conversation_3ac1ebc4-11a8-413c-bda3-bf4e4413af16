<?php
session_start();
require_once '../../includes/db_connect.php';
require_once '../../includes/env_loader.php';
require_once '../includes/auth_check.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['prompt']) || empty(trim($input['prompt']))) {
    http_response_code(400);
    echo json_encode(['error' => 'Prompt is required']);
    exit;
}

$prompt = trim($input['prompt']);
$type = $input['type'] ?? 'content'; // 'content', 'title', 'excerpt'

// Get Gemini API key from environment
$apiKey = $_ENV['GEMINI_API_KEY'] ?? '';

if (empty($apiKey)) {
    http_response_code(500);
    echo json_encode(['error' => 'Gemini API key not configured']);
    exit;
}

// Get prompt template from database
$prompt_sql = "SELECT prompt_text FROM ai_prompts WHERE prompt_type = '$type' AND is_active = 1 LIMIT 1";
$prompt_result = $conn->query($prompt_sql);

$systemPrompt = '';
if ($prompt_result && $prompt_result->num_rows > 0) {
    $prompt_row = $prompt_result->fetch_assoc();
    $systemPrompt = str_replace('{prompt}', $prompt, $prompt_row['prompt_text']);
} else {
    // Fallback to default prompts if none found in database
    switch ($type) {
        case 'title':
            $systemPrompt = "Generate a compelling, SEO-friendly blog post title for Lisbon Appliances (an appliance repair company in Lisbon). The title should be engaging, informative, and related to appliance repair, maintenance, or troubleshooting. Keep it under 60 characters for SEO. Based on this topic: " . $prompt;
            break;
        case 'excerpt':
            $systemPrompt = "Generate a compelling blog post excerpt/summary for Lisbon Appliances. This should be 1-2 sentences that hook the reader and summarize the main value of the article. Keep it under 160 characters for SEO meta descriptions. Based on this content: " . $prompt;
            break;
        case 'content':
            $systemPrompt = "Generate a comprehensive, informative blog post for Lisbon Appliances (an appliance repair company in Lisbon, South Africa). The content should be:
            - Professional and helpful
            - Include practical tips and advice
            - Be SEO-friendly with proper headings (H2, H3)
            - Include a call-to-action to contact Lisbon Appliances
            - Be formatted in HTML with proper tags
            - Around 800-1200 words
            - Include relevant keywords for appliance repair

            Topic: " . $prompt;
            break;
        default:
            $systemPrompt = "Generate helpful content for Lisbon Appliances blog about: " . $prompt;
    }
}

$fullPrompt = $systemPrompt;

// Prepare the request to Gemini API
$data = [
    'contents' => [
        [
            'parts' => [
                [
                    'text' => $fullPrompt
                ]
            ]
        ]
    ],
    'generationConfig' => [
        'temperature' => 0.7,
        'topK' => 40,
        'topP' => 0.95,
        'maxOutputTokens' => 2048,
    ]
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=" . $apiKey);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode !== 200) {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to generate content. Please check your API key and try again.']);
    exit;
}

$responseData = json_decode($response, true);

if (!isset($responseData['candidates'][0]['content']['parts'][0]['text'])) {
    http_response_code(500);
    echo json_encode(['error' => 'Invalid response from AI service']);
    exit;
}

$generatedContent = $responseData['candidates'][0]['content']['parts'][0]['text'];

// Clean up the content if it's HTML
if ($type === 'content') {
    // Ensure proper HTML formatting
    $generatedContent = trim($generatedContent);
    
    // Remove any markdown formatting and convert to HTML if needed
    $generatedContent = preg_replace('/\*\*(.*?)\*\*/', '<strong>$1</strong>', $generatedContent);
    $generatedContent = preg_replace('/\*(.*?)\*/', '<em>$1</em>', $generatedContent);
    
    // Ensure paragraphs are wrapped in <p> tags
    $lines = explode("\n\n", $generatedContent);
    $htmlContent = '';
    
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;
        
        // Check if it's a heading
        if (preg_match('/^#+\s+(.+)/', $line, $matches)) {
            $level = strlen(str_replace(' ', '', substr($line, 0, strpos($line, ' '))));
            $level = min($level + 1, 6); // Convert to H2-H6
            $htmlContent .= "<h{$level}>" . trim($matches[1]) . "</h{$level}>\n";
        } elseif (!preg_match('/^<[^>]+>/', $line)) {
            // If not already HTML, wrap in paragraph
            $htmlContent .= "<p>" . $line . "</p>\n";
        } else {
            $htmlContent .= $line . "\n";
        }
    }
    
    $generatedContent = $htmlContent;
}

echo json_encode([
    'success' => true,
    'content' => $generatedContent,
    'type' => $type
]);
?>
