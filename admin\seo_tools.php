<?php
session_start();
require_once '../includes/db_connect.php';
require_once '../includes/env_loader.php';
require_once 'includes/auth_check.php';

$success = '';
$error = '';

// Handle sitemap generation
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] == 'generate_sitemap') {
        // Force regenerate sitemap by accessing it
        $siteUrl = $_ENV['SITE_URL'] ?? 'http://localhost/lisbon-appliances';
        $sitemapUrl = rtrim($siteUrl, '/') . '/sitemap.php';
        
        // Test if sitemap is accessible
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'method' => 'GET'
            ]
        ]);
        
        $sitemapContent = @file_get_contents($sitemapUrl, false, $context);
        
        if ($sitemapContent !== false) {
            $success = 'Sitemap generated successfully! <a href="' . $sitemapUrl . '" target="_blank">View Sitemap</a>';
        } else {
            $error = 'Failed to generate sitemap. Please check your site URL configuration.';
        }
    }
}

// Get SEO statistics
$stats = [];

// Total published posts
$result = $conn->query("SELECT COUNT(*) as total FROM blog_posts WHERE status = 'published'");
$stats['total_posts'] = $result->fetch_assoc()['total'];

// Posts without meta description
$result = $conn->query("SELECT COUNT(*) as total FROM blog_posts WHERE status = 'published' AND (meta_description IS NULL OR meta_description = '')");
$stats['posts_without_meta'] = $result->fetch_assoc()['total'];

// Posts without meta keywords
$result = $conn->query("SELECT COUNT(*) as total FROM blog_posts WHERE status = 'published' AND (meta_keywords IS NULL OR meta_keywords = '')");
$stats['posts_without_keywords'] = $result->fetch_assoc()['total'];

// Posts without featured images
$result = $conn->query("SELECT COUNT(*) as total FROM blog_posts WHERE status = 'published' AND (image_url IS NULL OR image_url = '')");
$stats['posts_without_images'] = $result->fetch_assoc()['total'];

// Get posts that need SEO attention
$seo_issues_sql = "SELECT id, title, slug, 
                   CASE 
                       WHEN meta_description IS NULL OR meta_description = '' THEN 'Missing Meta Description'
                       WHEN meta_keywords IS NULL OR meta_keywords = '' THEN 'Missing Meta Keywords'
                       WHEN image_url IS NULL OR image_url = '' THEN 'Missing Featured Image'
                       WHEN LENGTH(title) > 60 THEN 'Title Too Long'
                       WHEN LENGTH(excerpt) > 160 THEN 'Excerpt Too Long'
                       ELSE 'OK'
                   END as issue
                   FROM blog_posts 
                   WHERE status = 'published' 
                   AND (meta_description IS NULL OR meta_description = '' 
                        OR meta_keywords IS NULL OR meta_keywords = ''
                        OR image_url IS NULL OR image_url = ''
                        OR LENGTH(title) > 60
                        OR LENGTH(excerpt) > 160)
                   ORDER BY publish_date DESC
                   LIMIT 10";
$seo_issues_result = $conn->query($seo_issues_sql);
$seo_issues = [];
if ($seo_issues_result->num_rows > 0) {
    while($row = $seo_issues_result->fetch_assoc()) {
        $seo_issues[] = $row;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO Tools - Lisbon Appliances Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/admin.css">
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">SEO Tools</h1>
                </div>

                <?php if ($success): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- SEO Overview -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-primary"><?php echo $stats['total_posts']; ?></h5>
                                <p class="card-text">Published Posts</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-warning"><?php echo $stats['posts_without_meta']; ?></h5>
                                <p class="card-text">Missing Meta Description</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-warning"><?php echo $stats['posts_without_keywords']; ?></h5>
                                <p class="card-text">Missing Keywords</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-info"><?php echo $stats['posts_without_images']; ?></h5>
                                <p class="card-text">Missing Images</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Sitemap Tools -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Sitemap Management</h5>
                            </div>
                            <div class="card-body">
                                <p>Generate and manage your website sitemap for better search engine indexing.</p>
                                
                                <form method="POST" class="mb-3">
                                    <input type="hidden" name="action" value="generate_sitemap">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-sitemap me-1"></i>Generate Sitemap
                                    </button>
                                </form>
                                
                                <div class="mt-3">
                                    <h6>Sitemap URLs:</h6>
                                    <ul class="list-unstyled">
                                        <li><a href="../sitemap.php" target="_blank">XML Sitemap</a></li>
                                        <li><small class="text-muted">Submit this URL to Google Search Console</small></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- SEO Issues -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">SEO Issues</h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($seo_issues)): ?>
                                    <div class="text-center text-success">
                                        <i class="fas fa-check-circle fa-3x mb-2"></i>
                                        <p>No SEO issues found! All posts are optimized.</p>
                                    </div>
                                <?php else: ?>
                                    <div class="list-group">
                                        <?php foreach ($seo_issues as $issue): ?>
                                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-1"><?php echo htmlspecialchars($issue['title']); ?></h6>
                                                    <small class="text-danger"><?php echo $issue['issue']; ?></small>
                                                </div>
                                                <a href="edit_post.php?id=<?php echo $issue['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                    Fix
                                                </a>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
