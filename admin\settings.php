<?php
session_start();
require_once '../includes/db_connect.php';
require_once 'includes/auth_check.php';

$success = '';
$error = '';

// Handle password change
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'change_password') {
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];
    
    if ($new_password !== $confirm_password) {
        $error = 'New passwords do not match';
    } elseif (strlen($new_password) < 6) {
        $error = 'New password must be at least 6 characters long';
    } else {
        // Verify current password
        $user_id = $_SESSION['admin_id'];
        $sql = "SELECT password FROM users WHERE id = $user_id";
        $result = $conn->query($sql);
        $user = $result->fetch_assoc();
        
        if (password_verify($current_password, $user['password'])) {
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            $update_sql = "UPDATE users SET password = '$hashed_password' WHERE id = $user_id";
            
            if ($conn->query($update_sql)) {
                $success = 'Password changed successfully!';
            } else {
                $error = 'Error updating password: ' . $conn->error;
            }
        } else {
            $error = 'Current password is incorrect';
        }
    }
}

// Get current user info
$user_id = $_SESSION['admin_id'];
$user_sql = "SELECT username, email, role, created_at FROM users WHERE id = $user_id";
$user_result = $conn->query($user_sql);
$user_info = $user_result->fetch_assoc();

// Get system stats
$stats = [];

// Total posts
$posts_sql = "SELECT COUNT(*) as total FROM blog_posts";
$posts_result = $conn->query($posts_sql);
$stats['total_posts'] = $posts_result->fetch_assoc()['total'];

// Published posts
$published_sql = "SELECT COUNT(*) as total FROM blog_posts WHERE status = 'published'";
$published_result = $conn->query($published_sql);
$stats['published_posts'] = $published_result->fetch_assoc()['total'];

// Draft posts
$draft_sql = "SELECT COUNT(*) as total FROM blog_posts WHERE status = 'draft'";
$draft_result = $conn->query($draft_sql);
$stats['draft_posts'] = $draft_result->fetch_assoc()['total'];

// Total categories
$categories_sql = "SELECT COUNT(*) as total FROM categories";
$categories_result = $conn->query($categories_sql);
$stats['total_categories'] = $categories_result->fetch_assoc()['total'];

// Recent activity
$activity_sql = "SELECT title, status, created_at, updated_at FROM blog_posts ORDER BY updated_at DESC LIMIT 5";
$activity_result = $conn->query($activity_sql);
$recent_activity = [];
if ($activity_result->num_rows > 0) {
    while($row = $activity_result->fetch_assoc()) {
        $recent_activity[] = $row;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - Lisbon Appliances Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/admin.css">
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Settings</h1>
                </div>

                <?php if ($success): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <!-- User Profile -->
                    <div class="col-lg-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-user me-2"></i>User Profile</h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-sm-4"><strong>Username:</strong></div>
                                    <div class="col-sm-8"><?php echo htmlspecialchars($user_info['username']); ?></div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-sm-4"><strong>Email:</strong></div>
                                    <div class="col-sm-8"><?php echo htmlspecialchars($user_info['email']); ?></div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-sm-4"><strong>Role:</strong></div>
                                    <div class="col-sm-8">
                                        <span class="badge bg-primary"><?php echo ucfirst($user_info['role']); ?></span>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-sm-4"><strong>Member Since:</strong></div>
                                    <div class="col-sm-8"><?php echo date('F j, Y', strtotime($user_info['created_at'])); ?></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Change Password -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-lock me-2"></i>Change Password</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <input type="hidden" name="action" value="change_password">
                                    
                                    <div class="mb-3">
                                        <label for="current_password" class="form-label">Current Password</label>
                                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="new_password" class="form-label">New Password</label>
                                        <input type="password" class="form-control" id="new_password" name="new_password" required minlength="6">
                                        <div class="form-text">Minimum 6 characters</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="confirm_password" class="form-label">Confirm New Password</label>
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required minlength="6">
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>Change Password
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- System Statistics -->
                    <div class="col-lg-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>System Statistics</h5>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6 mb-3">
                                        <div class="p-3 bg-primary text-white rounded">
                                            <i class="fas fa-file-alt fa-2x mb-2"></i>
                                            <h4><?php echo $stats['total_posts']; ?></h4>
                                            <small>Total Posts</small>
                                        </div>
                                    </div>
                                    <div class="col-6 mb-3">
                                        <div class="p-3 bg-success text-white rounded">
                                            <i class="fas fa-eye fa-2x mb-2"></i>
                                            <h4><?php echo $stats['published_posts']; ?></h4>
                                            <small>Published</small>
                                        </div>
                                    </div>
                                    <div class="col-6 mb-3">
                                        <div class="p-3 bg-warning text-white rounded">
                                            <i class="fas fa-edit fa-2x mb-2"></i>
                                            <h4><?php echo $stats['draft_posts']; ?></h4>
                                            <small>Drafts</small>
                                        </div>
                                    </div>
                                    <div class="col-6 mb-3">
                                        <div class="p-3 bg-info text-white rounded">
                                            <i class="fas fa-tags fa-2x mb-2"></i>
                                            <h4><?php echo $stats['total_categories']; ?></h4>
                                            <small>Categories</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Recent Activity -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-history me-2"></i>Recent Activity</h5>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($recent_activity)): ?>
                                    <div class="list-group list-group-flush">
                                        <?php foreach ($recent_activity as $activity): ?>
                                            <div class="list-group-item px-0">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div>
                                                        <h6 class="mb-1"><?php echo htmlspecialchars($activity['title']); ?></h6>
                                                        <small class="text-muted">
                                                            <span class="badge bg-<?php echo $activity['status'] == 'published' ? 'success' : 'warning'; ?>">
                                                                <?php echo ucfirst($activity['status']); ?>
                                                            </span>
                                                        </small>
                                                    </div>
                                                    <small class="text-muted">
                                                        <?php echo date('M j, g:i A', strtotime($activity['updated_at'])); ?>
                                                    </small>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php else: ?>
                                    <p class="text-muted mb-0">No recent activity</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- System Information -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-server me-2"></i>System Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <table class="table table-sm">
                                            <tr>
                                                <td><strong>PHP Version:</strong></td>
                                                <td><?php echo phpversion(); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>MySQL Version:</strong></td>
                                                <td><?php echo $conn->server_info; ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Server Software:</strong></td>
                                                <td><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <table class="table table-sm">
                                            <tr>
                                                <td><strong>Memory Limit:</strong></td>
                                                <td><?php echo ini_get('memory_limit'); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Upload Max Size:</strong></td>
                                                <td><?php echo ini_get('upload_max_filesize'); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Time Zone:</strong></td>
                                                <td><?php echo date_default_timezone_get(); ?></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Password confirmation validation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = this.value;
            
            if (newPassword !== confirmPassword) {
                this.setCustomValidity('Passwords do not match');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
