<?php
session_start();
require_once '../includes/db_connect.php';
require_once 'includes/auth_check.php';

$success = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        if ($_POST['action'] == 'update_prompt') {
            $id = (int)$_POST['prompt_id'];
            $prompt_text = $conn->real_escape_string($_POST['prompt_text']);
            $prompt_name = $conn->real_escape_string($_POST['prompt_name']);
            $description = $conn->real_escape_string($_POST['description']);
            $is_active = isset($_POST['is_active']) ? 1 : 0;
            
            $sql = "UPDATE ai_prompts SET prompt_text = '$prompt_text', prompt_name = '$prompt_name', description = '$description', is_active = $is_active WHERE id = $id";
            
            if ($conn->query($sql)) {
                $success = 'Prompt updated successfully!';
            } else {
                $error = 'Error updating prompt: ' . $conn->error;
            }
        } elseif ($_POST['action'] == 'add_prompt') {
            $prompt_type = $conn->real_escape_string($_POST['prompt_type']);
            $prompt_name = $conn->real_escape_string($_POST['prompt_name']);
            $prompt_text = $conn->real_escape_string($_POST['prompt_text']);
            $description = $conn->real_escape_string($_POST['description']);
            $is_active = isset($_POST['is_active']) ? 1 : 0;

            $sql = "INSERT INTO ai_prompts (prompt_type, prompt_name, prompt_text, description, is_active) VALUES ('$prompt_type', '$prompt_name', '$prompt_text', '$description', $is_active)";

            if ($conn->query($sql)) {
                $success = 'New prompt added successfully!';
            } else {
                $error = 'Error adding prompt: ' . $conn->error;
            }
        } elseif ($_POST['action'] == 'delete_prompt') {
            $id = (int)$_POST['prompt_id'];

            $sql = "DELETE FROM ai_prompts WHERE id = $id";

            if ($conn->query($sql)) {
                $success = 'Prompt deleted successfully!';
            } else {
                $error = 'Error deleting prompt: ' . $conn->error;
            }
        }
    }
}

// Get all AI prompts
$prompts_sql = "SELECT * FROM ai_prompts ORDER BY prompt_type, prompt_name";
$prompts_result = $conn->query($prompts_sql);
$prompts = [];
if ($prompts_result->num_rows > 0) {
    while($row = $prompts_result->fetch_assoc()) {
        $prompts[] = $row;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Settings - Lisbon Appliances Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/admin.css">
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">AI Assistant Settings</h1>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPromptModal">
                        <i class="fas fa-plus me-1"></i>Add New Prompt
                    </button>
                </div>

                <?php if ($success): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">AI Prompt Templates</h5>
                                <small class="text-muted">Configure prompts for different AI content generation features</small>
                            </div>
                            <div class="card-body">
                                <?php if (empty($prompts)): ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-robot fa-3x text-muted mb-3"></i>
                                        <h5>No AI prompts configured</h5>
                                        <p class="text-muted">Add your first AI prompt template to get started.</p>
                                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPromptModal">
                                            <i class="fas fa-plus me-1"></i>Add Prompt
                                        </button>
                                    </div>
                                <?php else: ?>
                                    <div class="accordion" id="promptsAccordion">
                                        <?php foreach ($prompts as $index => $prompt): ?>
                                            <div class="accordion-item">
                                                <h2 class="accordion-header" id="heading<?php echo $prompt['id']; ?>">
                                                    <button class="accordion-button <?php echo $index > 0 ? 'collapsed' : ''; ?>" type="button" 
                                                            data-bs-toggle="collapse" data-bs-target="#collapse<?php echo $prompt['id']; ?>">
                                                        <div class="d-flex justify-content-between align-items-center w-100 me-3">
                                                            <div>
                                                                <strong><?php echo htmlspecialchars($prompt['prompt_name']); ?></strong>
                                                                <span class="badge bg-<?php echo $prompt['is_active'] ? 'success' : 'secondary'; ?> ms-2">
                                                                    <?php echo $prompt['is_active'] ? 'Active' : 'Inactive'; ?>
                                                                </span>
                                                            </div>
                                                            <small class="text-muted"><?php echo ucfirst(str_replace('_', ' ', $prompt['prompt_type'])); ?></small>
                                                        </div>
                                                    </button>
                                                </h2>
                                                <div id="collapse<?php echo $prompt['id']; ?>" class="accordion-collapse collapse <?php echo $index == 0 ? 'show' : ''; ?>" 
                                                     data-bs-parent="#promptsAccordion">
                                                    <div class="accordion-body">
                                                        <form method="POST" class="prompt-form">
                                                            <input type="hidden" name="action" value="update_prompt">
                                                            <input type="hidden" name="prompt_id" value="<?php echo $prompt['id']; ?>">
                                                            
                                                            <div class="row mb-3">
                                                                <div class="col-md-6">
                                                                    <label class="form-label">Prompt Name</label>
                                                                    <input type="text" class="form-control" name="prompt_name" 
                                                                           value="<?php echo htmlspecialchars($prompt['prompt_name']); ?>" required>
                                                                </div>
                                                                <div class="col-md-6">
                                                                    <label class="form-label">Type</label>
                                                                    <input type="text" class="form-control" 
                                                                           value="<?php echo ucfirst(str_replace('_', ' ', $prompt['prompt_type'])); ?>" readonly>
                                                                </div>
                                                            </div>
                                                            
                                                            <div class="mb-3">
                                                                <label class="form-label">Description</label>
                                                                <input type="text" class="form-control" name="description" 
                                                                       value="<?php echo htmlspecialchars($prompt['description']); ?>">
                                                            </div>
                                                            
                                                            <div class="mb-3">
                                                                <label class="form-label">Prompt Template</label>
                                                                <textarea class="form-control" name="prompt_text" rows="8" required><?php echo htmlspecialchars($prompt['prompt_text']); ?></textarea>
                                                                <div class="form-text">Use {prompt} as a placeholder for user input</div>
                                                            </div>
                                                            
                                                            <div class="mb-3">
                                                                <div class="form-check">
                                                                    <input class="form-check-input" type="checkbox" name="is_active" 
                                                                           <?php echo $prompt['is_active'] ? 'checked' : ''; ?>>
                                                                    <label class="form-check-label">Active</label>
                                                                </div>
                                                            </div>
                                                            
                                                            <div class="d-flex justify-content-between">
                                                                <button type="submit" class="btn btn-primary">
                                                                    <i class="fas fa-save me-1"></i>Update Prompt
                                                                </button>
                                                                <button type="button" class="btn btn-outline-danger" 
                                                                        onclick="deletePrompt(<?php echo $prompt['id']; ?>)">
                                                                    <i class="fas fa-trash me-1"></i>Delete
                                                                </button>
                                                            </div>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Add Prompt Modal -->
    <div class="modal fade" id="addPromptModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New AI Prompt</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add_prompt">

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Prompt Type</label>
                                <select class="form-select" name="prompt_type" required>
                                    <option value="">Select Type</option>
                                    <option value="content">Full Article</option>
                                    <option value="title">Title Generator</option>
                                    <option value="excerpt">Excerpt Generator</option>
                                    <option value="outline">Article Outline</option>
                                    <option value="introduction">Introduction</option>
                                    <option value="conclusion">Conclusion</option>
                                    <option value="transcript_to_article">Transcript to Article</option>
                                    <option value="repurpose_article">Repurpose Article</option>
                                    <option value="custom">Custom Type</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Prompt Name</label>
                                <input type="text" class="form-control" name="prompt_name" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <input type="text" class="form-control" name="description"
                                   placeholder="Brief description of what this prompt does">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Prompt Template</label>
                            <textarea class="form-control" name="prompt_text" rows="8" required
                                      placeholder="Enter your prompt template here. Use {prompt} as placeholder for user input."></textarea>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_active" checked>
                                <label class="form-check-label">Active</label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add Prompt</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function deletePrompt(id) {
            if (confirm('Are you sure you want to delete this prompt? This action cannot be undone.')) {
                // Create a form to submit the delete request
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_prompt">
                    <input type="hidden" name="prompt_id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
