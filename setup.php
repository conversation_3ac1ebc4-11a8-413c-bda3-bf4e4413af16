<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lisbon Appliances Blog Setup</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #0056b3, #004494);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .setup-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            padding: 2rem;
            max-width: 600px;
            margin: 0 auto;
        }
        .setup-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .setup-header h1 {
            color: #0056b3;
            margin-bottom: 0.5rem;
        }
        .step {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 8px;
            border-left: 4px solid #0056b3;
            background: #f8f9fa;
        }
        .step.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .step.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .step.running {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="setup-container">
            <div class="setup-header">
                <i class="fas fa-tools fa-3x text-primary mb-3"></i>
                <h1>Lisbon Appliances Blog Setup</h1>
                <p class="text-muted">Initialize your blog management system</p>
            </div>

            <?php if (!isset($_POST['run_setup'])): ?>
                <div class="text-center">
                    <p>This setup will:</p>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>Create database tables</li>
                        <li><i class="fas fa-check text-success me-2"></i>Set up default categories</li>
                        <li><i class="fas fa-check text-success me-2"></i>Create admin user</li>
                        <li><i class="fas fa-check text-success me-2"></i>Configure AI prompts</li>
                        <li><i class="fas fa-check text-success me-2"></i>Initialize newsletter system</li>
                    </ul>
                    
                    <form method="POST">
                        <button type="submit" name="run_setup" class="btn btn-primary btn-lg">
                            <i class="fas fa-play me-2"></i>Run Setup
                        </button>
                    </form>
                </div>
            <?php else: ?>
                <div id="setup-progress">
                    <div class="step running">
                        <h5><i class="fas fa-spinner fa-spin me-2"></i>Running Database Setup...</h5>
                        <p>Creating tables and initial data...</p>
                    </div>
                </div>

                <script>
                    // Simulate setup progress
                    setTimeout(function() {
                        fetch('includes/setup_database.php')
                        .then(response => response.text())
                        .then(data => {
                            const progressDiv = document.getElementById('setup-progress');
                            
                            if (data.includes('Error')) {
                                progressDiv.innerHTML = `
                                    <div class="step error">
                                        <h5><i class="fas fa-times me-2"></i>Setup Failed</h5>
                                        <p>There was an error during setup:</p>
                                        <pre class="small">${data}</pre>
                                        <a href="setup.php" class="btn btn-primary mt-2">Try Again</a>
                                    </div>
                                `;
                            } else {
                                progressDiv.innerHTML = `
                                    <div class="step success">
                                        <h5><i class="fas fa-check me-2"></i>Database Setup Complete</h5>
                                        <p>All tables and initial data have been created successfully.</p>
                                    </div>
                                    <div class="step success">
                                        <h5><i class="fas fa-user me-2"></i>Admin Account Created</h5>
                                        <p><strong>Username:</strong> admin<br>
                                        <strong>Password:</strong> admin123<br>
                                        <small class="text-warning">Please change this password after first login!</small></p>
                                    </div>
                                    <div class="step success">
                                        <h5><i class="fas fa-robot me-2"></i>AI System Ready</h5>
                                        <p>AI prompts and content generation system configured.</p>
                                    </div>
                                    <div class="step success">
                                        <h5><i class="fas fa-envelope me-2"></i>Newsletter System Ready</h5>
                                        <p>Newsletter subscription system is now active.</p>
                                    </div>
                                    <div class="text-center mt-4">
                                        <h4 class="text-success mb-3">Setup Complete!</h4>
                                        <p>Your blog management system is ready to use.</p>
                                        <div class="d-grid gap-2">
                                            <a href="admin/login.php" class="btn btn-primary btn-lg">
                                                <i class="fas fa-sign-in-alt me-2"></i>Go to Admin Dashboard
                                            </a>
                                            <a href="index.php" class="btn btn-outline-primary">
                                                <i class="fas fa-home me-2"></i>View Website
                                            </a>
                                        </div>
                                    </div>
                                `;
                            }
                        })
                        .catch(error => {
                            const progressDiv = document.getElementById('setup-progress');
                            progressDiv.innerHTML = `
                                <div class="step error">
                                    <h5><i class="fas fa-times me-2"></i>Setup Failed</h5>
                                    <p>An error occurred during setup. Please check your database configuration.</p>
                                    <a href="setup.php" class="btn btn-primary mt-2">Try Again</a>
                                </div>
                            `;
                        });
                    }, 1000);
                </script>
            <?php endif; ?>

            <div class="mt-4 text-center">
                <small class="text-muted">
                    Make sure your database credentials are configured in <code>includes/db_connect.php</code>
                </small>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
