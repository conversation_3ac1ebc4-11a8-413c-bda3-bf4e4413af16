<?php
require_once 'db_connect.php';

// Create tables if they don't exist
$sql = "
CREATE TABLE IF NOT EXISTS users (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS categories (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    slug VARCHAR(50) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS blog_posts (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE,
    excerpt TEXT,
    content LONGTEXT NOT NULL,
    meta_keywords TEXT,
    meta_description TEXT,
    image_url VARCHAR(255),
    author_id INT(11),
    category_id INT(11),
    featured BOOLEAN DEFAULT 0,
    status ENUM('draft', 'published') DEFAULT 'draft',
    publish_date TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
);

CREATE TABLE IF NOT EXISTS tags (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    slug VARCHAR(50) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS post_tags (
    post_id INT(11),
    tag_id INT(11),
    PRIMARY KEY (post_id, tag_id),
    FOREIGN KEY (post_id) REFERENCES blog_posts(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS ai_prompts (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    prompt_type VARCHAR(50) NOT NULL UNIQUE,
    prompt_name VARCHAR(100) NOT NULL,
    prompt_text TEXT NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS newsletter_subscribers (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    name VARCHAR(100),
    status ENUM('active', 'unsubscribed') DEFAULT 'active',
    subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    unsubscribed_at TIMESTAMP NULL,
    ip_address VARCHAR(45),
    user_agent TEXT
);
";

if ($conn->multi_query($sql)) {
    do {
        // Store first result set
        if ($result = $conn->store_result()) {
            $result->free();
        }
        // Check if there are more result sets
    } while ($conn->more_results() && $conn->next_result());
    
    echo "Database tables created successfully";
} else {
    echo "Error creating tables: " . $conn->error;
}

// Insert default admin user if not exists
$username = "admin";
$password = password_hash("admin123", PASSWORD_DEFAULT); // Change this!
$email = "<EMAIL>";

$stmt = $conn->prepare("SELECT id FROM users WHERE username = ?");
$stmt->bind_param("s", $username);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    $stmt = $conn->prepare("INSERT INTO users (username, password, email) VALUES (?, ?, ?)");
    $stmt->bind_param("sss", $username, $password, $email);
    
    if ($stmt->execute()) {
        echo "<br>Default admin user created";
    } else {
        echo "<br>Error creating admin user: " . $stmt->error;
    }
}

// Insert default categories
$categories = [
    ["Refrigerator Repair", "refrigerator-repair"],
    ["Washing Machine Repair", "washing-machine-repair"],
    ["Cold Room Maintenance", "cold-room-maintenance"],
    ["DIY Tips", "diy-tips"],
    ["Appliance Guides", "appliance-guides"]
];

foreach ($categories as $category) {
    $name = $category[0];
    $slug = $category[1];
    
    $stmt = $conn->prepare("SELECT id FROM categories WHERE slug = ?");
    $stmt->bind_param("s", $slug);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        $stmt = $conn->prepare("INSERT INTO categories (name, slug) VALUES (?, ?)");
        $stmt->bind_param("ss", $name, $slug);
        $stmt->execute();
    }
}

// Insert default AI prompts
$defaultPrompts = [
    [
        'content',
        'Full Article Generator',
        'Generate a comprehensive, informative blog post for Lisbon Appliances (an appliance repair company in Lisbon, South Africa). The content should be:
- Professional and helpful
- Include practical tips and advice
- Be SEO-friendly with proper headings (H2, H3)
- Include a call-to-action to contact Lisbon Appliances
- Be formatted in HTML with proper tags
- Around 800-1200 words
- Include relevant keywords for appliance repair

Topic: {prompt}',
        'Generates complete blog articles with SEO optimization'
    ],
    [
        'title',
        'SEO Title Generator',
        'Generate a compelling, SEO-friendly blog post title for Lisbon Appliances (an appliance repair company in Lisbon). The title should be engaging, informative, and related to appliance repair, maintenance, or troubleshooting. Keep it under 60 characters for SEO. Based on this topic: {prompt}',
        'Creates SEO-optimized titles under 60 characters'
    ],
    [
        'excerpt',
        'Meta Description Generator',
        'Generate a compelling blog post excerpt/summary for Lisbon Appliances. This should be 1-2 sentences that hook the reader and summarize the main value of the article. Keep it under 160 characters for SEO meta descriptions. Based on this content: {prompt}',
        'Creates engaging excerpts for SEO meta descriptions'
    ],
    [
        'transcript_to_article',
        'Transcript to Article Converter',
        'Convert the following transcript into a well-structured, professional blog article for Lisbon Appliances. Format it with proper headings, paragraphs, and include relevant appliance repair tips. Make it engaging and informative for homeowners:

Transcript: {prompt}',
        'Converts audio/video transcripts into structured articles'
    ],
    [
        'repurpose_article',
        'Article Repurposing Tool',
        'Repurpose the following article content for a different format or audience while maintaining the core information about appliance repair. Make it fresh and engaging:

Original content: {prompt}',
        'Repurposes existing content for different formats'
    ],
    [
        'outline',
        'Article Outline Generator',
        'Create a detailed outline for a blog post about: {prompt}. Include main headings, subheadings, and key points to cover. Focus on appliance repair and maintenance topics relevant to Lisbon Appliances customers.',
        'Creates structured outlines for blog posts'
    ],
    [
        'introduction',
        'Introduction Generator',
        'Write an engaging introduction for a blog post about: {prompt}. Make it hook the reader and clearly state what they will learn. Keep it relevant to appliance repair and Lisbon Appliances services.',
        'Creates compelling introductions for articles'
    ],
    [
        'conclusion',
        'Conclusion Generator',
        'Write a strong conclusion for a blog post about: {prompt}. Summarize key points and include a call-to-action for Lisbon Appliances services. Encourage readers to contact for professional help.',
        'Creates effective conclusions with call-to-actions'
    ]
];

foreach ($defaultPrompts as $prompt) {
    $type = $prompt[0];
    $name = $prompt[1];
    $text = $prompt[2];
    $description = $prompt[3];

    $stmt = $conn->prepare("SELECT id FROM ai_prompts WHERE prompt_type = ?");
    $stmt->bind_param("s", $type);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows == 0) {
        $stmt = $conn->prepare("INSERT INTO ai_prompts (prompt_type, prompt_name, prompt_text, description) VALUES (?, ?, ?, ?)");
        $stmt->bind_param("ssss", $type, $name, $text, $description);
        $stmt->execute();
    }
}

$conn->close();
?>