<?php
session_start();
require_once '../includes/db_connect.php';
require_once 'includes/auth_check.php';

$success = '';
$error = '';
$post = null;

// Get post ID
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header("Location: posts.php");
    exit;
}

$post_id = (int)$_GET['id'];

// Get post data
$post_sql = "SELECT * FROM blog_posts WHERE id = $post_id";
$post_result = $conn->query($post_sql);

if ($post_result->num_rows == 0) {
    header("Location: posts.php");
    exit;
}

$post = $post_result->fetch_assoc();

// Get categories
$categories_sql = "SELECT id, name FROM categories ORDER BY name";
$categories_result = $conn->query($categories_sql);
$categories = [];
if ($categories_result->num_rows > 0) {
    while($row = $categories_result->fetch_assoc()) {
        $categories[] = $row;
    }
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $title = $conn->real_escape_string($_POST['title']);
    $slug = $conn->real_escape_string($_POST['slug']);
    $excerpt = $conn->real_escape_string($_POST['excerpt']);
    $content = $conn->real_escape_string($_POST['content']);
    $category_id = (int)$_POST['category_id'];
    $status = $conn->real_escape_string($_POST['status']);
    $image_url = $conn->real_escape_string($_POST['image_url']);
    
    // Generate slug if empty
    if (empty($slug)) {
        $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $title)));
    }
    
    // Check if slug exists (excluding current post)
    $slug_check = $conn->query("SELECT id FROM blog_posts WHERE slug = '$slug' AND id != $post_id");
    if ($slug_check->num_rows > 0) {
        $slug .= '-' . time();
    }
    
    // Update publish date if status changed to published
    $publish_date_update = '';
    if ($status == 'published' && $post['status'] != 'published') {
        $publish_date_update = ", publish_date = NOW()";
    } elseif ($status != 'published') {
        $publish_date_update = ", publish_date = NULL";
    }
    
    $sql = "UPDATE blog_posts SET 
            title = '$title', 
            slug = '$slug', 
            excerpt = '$excerpt', 
            content = '$content', 
            category_id = $category_id, 
            status = '$status', 
            image_url = '$image_url', 
            updated_at = NOW()
            $publish_date_update
            WHERE id = $post_id";
    
    if ($conn->query($sql)) {
        $success = 'Post updated successfully!';
        if ($status == 'published') {
            $success .= ' <a href="../article.php?slug=' . $slug . '" target="_blank">View Post</a>';
        }
        // Refresh post data
        $post_result = $conn->query("SELECT * FROM blog_posts WHERE id = $post_id");
        $post = $post_result->fetch_assoc();
    } else {
        $error = 'Error updating post: ' . $conn->error;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Post - Lisbon Appliances Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/admin.css">
    <style>
        .editor-container {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            min-height: 400px;
        }
        .editor-toolbar {
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 0.5rem;
            border-radius: 8px 8px 0 0;
        }
        .editor-content {
            padding: 1rem;
            min-height: 350px;
            outline: none;
        }
    </style>
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Edit Post</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="posts.php" class="btn btn-sm btn-outline-secondary me-2">
                            <i class="fas fa-arrow-left me-1"></i>Back to Posts
                        </a>
                        <?php if ($post['status'] == 'published'): ?>
                            <a href="../article.php?slug=<?php echo htmlspecialchars($post['slug']); ?>" 
                               class="btn btn-sm btn-outline-primary" target="_blank">
                                <i class="fas fa-eye me-1"></i>View Post
                            </a>
                        <?php endif; ?>
                    </div>
                </div>

                <?php if ($success): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <form method="POST" id="postForm">
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card mb-4">
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="title" class="form-label">Title</label>
                                        <input type="text" class="form-control" id="title" name="title" required 
                                               value="<?php echo htmlspecialchars($post['title']); ?>"
                                               onkeyup="generateSlug()" placeholder="Enter post title">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="slug" class="form-label">Slug</label>
                                        <input type="text" class="form-control" id="slug" name="slug" 
                                               value="<?php echo htmlspecialchars($post['slug']); ?>"
                                               placeholder="auto-generated-from-title">
                                        <div class="form-text">URL-friendly version of the title</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="excerpt" class="form-label">Excerpt</label>
                                        <textarea class="form-control" id="excerpt" name="excerpt" rows="3" 
                                                  placeholder="Brief description of the post"><?php echo htmlspecialchars($post['excerpt']); ?></textarea>
                                        <div class="form-text">Brief summary for SEO and previews</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="content" class="form-label">Content</label>
                                        <div class="editor-container">
                                            <div class="editor-toolbar">
                                                <button type="button" class="btn btn-sm btn-outline-secondary me-1" onclick="formatText('bold')">
                                                    <i class="fas fa-bold"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary me-1" onclick="formatText('italic')">
                                                    <i class="fas fa-italic"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary me-1" onclick="formatText('underline')">
                                                    <i class="fas fa-underline"></i>
                                                </button>
                                                <div class="vr mx-2"></div>
                                                <button type="button" class="btn btn-sm btn-outline-secondary me-1" onclick="formatText('insertOrderedList')">
                                                    <i class="fas fa-list-ol"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary me-1" onclick="formatText('insertUnorderedList')">
                                                    <i class="fas fa-list-ul"></i>
                                                </button>
                                            </div>
                                            <div class="editor-content" contenteditable="true" id="contentEditor" 
                                                 onkeyup="updateHiddenContent()"><?php echo $post['content']; ?></div>
                                        </div>
                                        <textarea name="content" id="hiddenContent" style="display: none;"><?php echo htmlspecialchars($post['content']); ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-4">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">Publish</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="status" class="form-label">Status</label>
                                        <select class="form-select" id="status" name="status">
                                            <option value="draft" <?php echo $post['status'] == 'draft' ? 'selected' : ''; ?>>Draft</option>
                                            <option value="published" <?php echo $post['status'] == 'published' ? 'selected' : ''; ?>>Published</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="category_id" class="form-label">Category</label>
                                        <select class="form-select" id="category_id" name="category_id" required>
                                            <option value="">Select Category</option>
                                            <?php foreach ($categories as $category): ?>
                                                <option value="<?php echo $category['id']; ?>" 
                                                        <?php echo $post['category_id'] == $category['id'] ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($category['name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <?php if ($post['publish_date']): ?>
                                        <div class="mb-3">
                                            <small class="text-muted">
                                                <i class="fas fa-calendar me-1"></i>
                                                Published: <?php echo date('M j, Y g:i A', strtotime($post['publish_date'])); ?>
                                            </small>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="mb-3">
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            Last updated: <?php echo date('M j, Y g:i A', strtotime($post['updated_at'])); ?>
                                        </small>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i>Update Post
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" onclick="previewPost()">
                                            <i class="fas fa-eye me-1"></i>Preview
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Featured Image</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="image_url" class="form-label">Image URL</label>
                                        <input type="url" class="form-control" id="image_url" name="image_url" 
                                               value="<?php echo htmlspecialchars($post['image_url']); ?>"
                                               placeholder="https://example.com/image.jpg" onchange="previewImage()">
                                    </div>
                                    <div id="imagePreview" class="text-center" <?php echo $post['image_url'] ? '' : 'style="display: none;"'; ?>>
                                        <img id="previewImg" src="<?php echo htmlspecialchars($post['image_url']); ?>" 
                                             alt="Preview" class="img-fluid rounded" style="max-height: 200px;">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function generateSlug() {
            const title = document.getElementById('title').value;
            const slug = title.toLowerCase()
                .replace(/[^a-z0-9 -]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');
            document.getElementById('slug').value = slug;
        }
        
        function formatText(command) {
            document.execCommand(command, false, null);
            updateHiddenContent();
        }
        
        function updateHiddenContent() {
            const content = document.getElementById('contentEditor').innerHTML;
            document.getElementById('hiddenContent').value = content;
        }
        
        function previewImage() {
            const url = document.getElementById('image_url').value;
            const preview = document.getElementById('imagePreview');
            const img = document.getElementById('previewImg');
            
            if (url) {
                img.src = url;
                preview.style.display = 'block';
            } else {
                preview.style.display = 'none';
            }
        }
        
        function previewPost() {
            const title = document.getElementById('title').value;
            const content = document.getElementById('contentEditor').innerHTML;
            
            if (!title || !content) {
                alert('Please fill in title and content first');
                return;
            }
            
            const previewWindow = window.open('', '_blank');
            previewWindow.document.write(`
                <html>
                <head>
                    <title>${title} - Preview</title>
                    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
                    <style>
                        body { padding: 2rem; }
                        .preview-header { background: #f8f9fa; padding: 1rem; border-radius: 8px; margin-bottom: 2rem; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="preview-header">
                            <h6 class="text-muted mb-0">PREVIEW MODE</h6>
                        </div>
                        <h1>${title}</h1>
                        <div class="content">${content}</div>
                    </div>
                </body>
                </html>
            `);
        }
        
        // Form submission handler
        document.getElementById('postForm').addEventListener('submit', function(e) {
            updateHiddenContent();
            
            const title = document.getElementById('title').value.trim();
            const content = document.getElementById('hiddenContent').value.trim();
            
            if (!title || !content) {
                e.preventDefault();
                alert('Please fill in all required fields');
                return;
            }
        });
        
        // Initialize content on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateHiddenContent();
        });
    </script>
</body>
</html>
