<?php
session_start();
require_once '../includes/db_connect.php';
require_once 'includes/auth_check.php';

$success = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        if ($_POST['action'] == 'add_tag') {
            $name = trim($conn->real_escape_string($_POST['name']));
            $slug = trim($conn->real_escape_string($_POST['slug']));
            
            if (empty($name)) {
                $error = 'Tag name is required';
            } else {
                // Generate slug if empty
                if (empty($slug)) {
                    $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $name)));
                }
                
                // Check if tag exists
                $check_sql = "SELECT id FROM tags WHERE name = '$name' OR slug = '$slug'";
                $check_result = $conn->query($check_sql);
                
                if ($check_result->num_rows > 0) {
                    $error = 'Tag with this name or slug already exists';
                } else {
                    $sql = "INSERT INTO tags (name, slug) VALUES ('$name', '$slug')";
                    if ($conn->query($sql)) {
                        $success = 'Tag added successfully!';
                    } else {
                        $error = 'Error adding tag: ' . $conn->error;
                    }
                }
            }
        } elseif ($_POST['action'] == 'edit_tag') {
            $id = (int)$_POST['tag_id'];
            $name = trim($conn->real_escape_string($_POST['name']));
            $slug = trim($conn->real_escape_string($_POST['slug']));
            
            if (empty($name)) {
                $error = 'Tag name is required';
            } else {
                // Generate slug if empty
                if (empty($slug)) {
                    $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $name)));
                }
                
                // Check if tag exists (excluding current tag)
                $check_sql = "SELECT id FROM tags WHERE (name = '$name' OR slug = '$slug') AND id != $id";
                $check_result = $conn->query($check_sql);
                
                if ($check_result->num_rows > 0) {
                    $error = 'Tag with this name or slug already exists';
                } else {
                    $sql = "UPDATE tags SET name = '$name', slug = '$slug' WHERE id = $id";
                    if ($conn->query($sql)) {
                        $success = 'Tag updated successfully!';
                    } else {
                        $error = 'Error updating tag: ' . $conn->error;
                    }
                }
            }
        } elseif ($_POST['action'] == 'delete_tag') {
            $id = (int)$_POST['tag_id'];
            
            // First remove tag associations
            $conn->query("DELETE FROM post_tags WHERE tag_id = $id");
            
            // Then delete the tag
            $sql = "DELETE FROM tags WHERE id = $id";
            if ($conn->query($sql)) {
                $success = 'Tag deleted successfully!';
            } else {
                $error = 'Error deleting tag: ' . $conn->error;
            }
        }
    }
}

// Get all tags with post counts
$tags_sql = "SELECT t.*, COUNT(pt.post_id) as post_count 
              FROM tags t 
              LEFT JOIN post_tags pt ON t.id = pt.tag_id 
              LEFT JOIN blog_posts p ON pt.post_id = p.id AND p.status = 'published'
              GROUP BY t.id 
              ORDER BY t.name";
$tags_result = $conn->query($tags_sql);
$tags = [];
if ($tags_result->num_rows > 0) {
    while($row = $tags_result->fetch_assoc()) {
        $tags[] = $row;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tags - Lisbon Appliances Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/admin.css">
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Tags</h1>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTagModal">
                        <i class="fas fa-plus me-1"></i>Add New Tag
                    </button>
                </div>

                <?php if ($success): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">All Tags</h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($tags)): ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                                        <h5>No tags found</h5>
                                        <p class="text-muted">Create your first tag to organize your blog posts.</p>
                                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTagModal">
                                            <i class="fas fa-plus me-1"></i>Add Tag
                                        </button>
                                    </div>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Name</th>
                                                    <th>Slug</th>
                                                    <th>Posts</th>
                                                    <th>Created</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($tags as $tag): ?>
                                                    <tr>
                                                        <td>
                                                            <strong><?php echo htmlspecialchars($tag['name']); ?></strong>
                                                        </td>
                                                        <td>
                                                            <code><?php echo htmlspecialchars($tag['slug']); ?></code>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-primary"><?php echo $tag['post_count']; ?></span>
                                                        </td>
                                                        <td>
                                                            <?php echo date('M j, Y', strtotime($tag['created_at'])); ?>
                                                        </td>
                                                        <td>
                                                            <button type="button" class="btn btn-sm btn-outline-primary" 
                                                                    onclick="editTag(<?php echo $tag['id']; ?>, '<?php echo htmlspecialchars($tag['name'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($tag['slug'], ENT_QUOTES); ?>')">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                                    onclick="deleteTag(<?php echo $tag['id']; ?>, '<?php echo htmlspecialchars($tag['name'], ENT_QUOTES); ?>')">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Add Tag Modal -->
    <div class="modal fade" id="addTagModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Tag</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add_tag">

                        <div class="mb-3">
                            <label for="tagName" class="form-label">Tag Name</label>
                            <input type="text" class="form-control" id="tagName" name="name" required
                                   onkeyup="generateTagSlug()">
                        </div>

                        <div class="mb-3">
                            <label for="tagSlug" class="form-label">Slug</label>
                            <input type="text" class="form-control" id="tagSlug" name="slug"
                                   placeholder="Auto-generated from name">
                            <div class="form-text">URL-friendly version of the name</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add Tag</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Tag Modal -->
    <div class="modal fade" id="editTagModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Tag</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="edit_tag">
                        <input type="hidden" name="tag_id" id="editTagId">

                        <div class="mb-3">
                            <label for="editTagName" class="form-label">Tag Name</label>
                            <input type="text" class="form-control" id="editTagName" name="name" required
                                   onkeyup="generateEditTagSlug()">
                        </div>

                        <div class="mb-3">
                            <label for="editTagSlug" class="form-label">Slug</label>
                            <input type="text" class="form-control" id="editTagSlug" name="slug">
                            <div class="form-text">URL-friendly version of the name</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Tag</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function generateTagSlug() {
            const name = document.getElementById('tagName').value;
            const slug = name.toLowerCase()
                .replace(/[^a-z0-9 -]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');
            document.getElementById('tagSlug').value = slug;
        }

        function generateEditTagSlug() {
            const name = document.getElementById('editTagName').value;
            const slug = name.toLowerCase()
                .replace(/[^a-z0-9 -]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');
            document.getElementById('editTagSlug').value = slug;
        }

        function editTag(id, name, slug) {
            document.getElementById('editTagId').value = id;
            document.getElementById('editTagName').value = name;
            document.getElementById('editTagSlug').value = slug;

            const modal = new bootstrap.Modal(document.getElementById('editTagModal'));
            modal.show();
        }

        function deleteTag(id, name) {
            if (confirm(`Are you sure you want to delete the tag "${name}"? This will remove it from all associated posts.`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_tag">
                    <input type="hidden" name="tag_id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
