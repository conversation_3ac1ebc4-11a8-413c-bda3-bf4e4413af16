:root {
    --primary: #0056b3;
    --secondary: #6c757d;
    --success: #198754;
    --danger: #dc3545;
    --warning: #ffc107;
    --info: #0dcaf0;
    --light: #f8f9fa;
    --dark: #212529;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    background-color: #fff;
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: .5rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    margin: 0.125rem 0.5rem;
    transition: all 0.2s ease;
}

.sidebar .nav-link:hover {
    color: var(--primary);
    background-color: rgba(0, 86, 179, 0.1);
}

.sidebar .nav-link.active {
    color: var(--primary);
    background-color: rgba(0, 86, 179, 0.15);
    font-weight: 600;
}

.sidebar .nav-link i {
    margin-right: 0.5rem;
    width: 16px;
    text-align: center;
}

/* Header Styles */
.navbar-brand {
    padding-top: .75rem;
    padding-bottom: .75rem;
    font-size: 1rem;
    background-color: rgba(0, 0, 0, .25);
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .25);
}

.navbar .navbar-toggler {
    top: .25rem;
    right: 1rem;
}

/* Main Content */
main {
    padding-top: 48px;
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
}

/* Tables */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
}

/* Buttons */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: var(--primary);
    border-color: var(--primary);
}

.btn-primary:hover {
    background-color: #004494;
    border-color: #004494;
    transform: translateY(-1px);
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* Status Badges */
.badge {
    font-weight: 500;
    border-radius: 0.375rem;
}

/* Forms */
.form-control {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem rgba(0, 86, 179, 0.25);
}

.form-select {
    border-radius: 0.375rem;
}

/* Alerts */
.alert {
    border: none;
    border-radius: 0.5rem;
}

.alert-success {
    background-color: #d1e7dd;
    color: #0f5132;
}

.alert-danger {
    background-color: #f8d7da;
    color: #842029;
}

.alert-warning {
    background-color: #fff3cd;
    color: #664d03;
}

.alert-info {
    background-color: #d1ecf1;
    color: #055160;
}

/* Dashboard Stats */
.stat-card {
    background: linear-gradient(135deg, var(--primary), #004494);
    color: white;
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-card i {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.stat-card h3 {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-card p {
    margin: 0;
    opacity: 0.9;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Responsive */
@media (max-width: 767.98px) {
    .sidebar {
        top: 5rem;
    }
    
    main {
        padding-top: 5rem;
    }
}

/* Custom Scrollbar */
.sidebar-sticky::-webkit-scrollbar {
    width: 6px;
}

.sidebar-sticky::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.sidebar-sticky::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.sidebar-sticky::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

/* Utility Classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.border-radius-lg {
    border-radius: 0.5rem !important;
}

/* Force light mode and ensure visibility */
body {
    background-color: #f8f9fa !important;
    color: #212529 !important;
}

.container-fluid {
    background-color: #f8f9fa !important;
}

.card {
    background-color: white !important;
    color: #212529 !important;
    border: 1px solid #dee2e6 !important;
}

.sidebar {
    background-color: #f8f9fa !important;
    border-right: 1px solid #dee2e6 !important;
}

.sidebar .nav-link {
    color: #333 !important;
}

.sidebar .nav-link:hover {
    color: var(--primary) !important;
    background-color: rgba(0, 86, 179, 0.1) !important;
}

.table {
    background-color: white !important;
    color: #212529 !important;
}

.table th {
    background-color: #f8f9fa !important;
    color: #495057 !important;
}

.form-control {
    background-color: white !important;
    color: #212529 !important;
}

.form-label {
    color: #212529 !important;
}

.text-muted {
    color: #6c757d !important;
}

.navbar {
    background-color: #343a40 !important;
}

.navbar-brand {
    color: white !important;
}

.nav-link {
    color: white !important;
}

/* Override any dark mode styles */
* {
    color: inherit !important;
}

/* Ensure all text is visible */
h1, h2, h3, h4, h5, h6, p, span, div, td, th, label {
    color: inherit !important;
}

/* Dark mode support - DISABLED to force light mode */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #f8f9fa !important;
        color: #212529 !important;
    }

    .sidebar {
        background-color: #f8f9fa !important;
        border-right: 1px solid #dee2e6 !important;
    }

    .sidebar .nav-link {
        color: #333 !important;
    }

    .sidebar .nav-link:hover {
        background-color: rgba(0, 86, 179, 0.1) !important;
    }

    .card {
        background-color: white !important;
        color: #212529 !important;
    }

    .table th {
        background-color: #f8f9fa !important;
        color: #495057 !important;
    }
}

