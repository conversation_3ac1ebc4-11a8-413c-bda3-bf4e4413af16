<?php
require_once 'includes/db_connect.php';

// Get article slug from URL
$slug = isset($_GET['slug']) ? $conn->real_escape_string($_GET['slug']) : '';

if (empty($slug)) {
    header("Location: index.php");
    exit;
}

// Get article data
$article_sql = "SELECT p.*, c.name as category_name, u.username as author_name 
                FROM blog_posts p 
                LEFT JOIN categories c ON p.category_id = c.id 
                LEFT JOIN users u ON p.author_id = u.id 
                WHERE p.slug = '$slug' AND p.status = 'published'";
$article_result = $conn->query($article_sql);

if ($article_result->num_rows == 0) {
    header("HTTP/1.0 404 Not Found");
    include '404.php';
    exit;
}

$article = $article_result->fetch_assoc();


?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($article['title']); ?> - Lisbon Appliances</title>
    <meta name="description" content="<?php echo htmlspecialchars($article['excerpt'] ?: substr(strip_tags($article['content']), 0, 160)); ?>">
    <meta name="keywords" content="appliance repair, Lisbon, <?php echo htmlspecialchars($article['category_name']); ?>">
    
    <!-- Open Graph -->
    <meta property="og:title" content="<?php echo htmlspecialchars($article['title']); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars($article['excerpt'] ?: substr(strip_tags($article['content']), 0, 160)); ?>">
    <meta property="og:image" content="<?php echo htmlspecialchars($article['image_url'] ?: 'https://lisbonappliances.com/default-og-image.jpg'); ?>">
    <meta property="og:url" content="https://lisbonappliances.com/article.php?slug=<?php echo htmlspecialchars($article['slug']); ?>">
    <meta property="og:type" content="article">
    
    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo htmlspecialchars($article['title']); ?>">
    <meta name="twitter:description" content="<?php echo htmlspecialchars($article['excerpt'] ?: substr(strip_tags($article['content']), 0, 160)); ?>">
    <meta name="twitter:image" content="<?php echo htmlspecialchars($article['image_url'] ?: 'https://lisbonappliances.com/default-og-image.jpg'); ?>">
    
    <link rel="canonical" href="https://lisbonappliances.com/article.php?slug=<?php echo htmlspecialchars($article['slug']); ?>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    
    <style>
        :root {
            --primary: #0056b3;
            --secondary: #6c757d;
            --accent: #ffc107;
            --light: #f8f9fa;
            --dark: #343a40;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            color: #333;
            line-height: 1.6;
            overflow-x: hidden;
            padding-top: 70px;
        }
        
        .navbar {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 15px 0;
        }
        
        .navbar-brand {
            font-weight: bold;
            color: var(--primary) !important;
        }
        
        .nav-link {
            color: #333 !important;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .nav-link:hover {
            color: var(--primary) !important;
        }
        
        .article-hero {
            background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('<?php echo htmlspecialchars($article['image_url'] ?: 'default-article-bg.jpg'); ?>') center/cover no-repeat;
            height: 60vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            position: relative;
        }
        
        .article-hero-content h1 {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .article-meta {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            padding: 1rem;
            border-radius: 10px;
            margin-top: 2rem;
        }
        
        .article-content {
            font-size: 1.1rem;
            line-height: 1.8;
        }
        
        .article-content h2 {
            color: var(--primary);
            margin-top: 2rem;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        
        .article-content h3 {
            color: var(--dark);
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
        }
        
        .article-content ul, .article-content ol {
            margin: 1rem 0;
            padding-left: 2rem;
        }
        
        .article-content li {
            margin-bottom: 0.5rem;
        }
        
        .article-content blockquote {
            border-left: 4px solid var(--primary);
            background: var(--light);
            padding: 1rem 1.5rem;
            margin: 1.5rem 0;
            font-style: italic;
        }
        
        .article-cta {
            background: linear-gradient(135deg, var(--primary), #004494);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin: 3rem 0;
        }
        
        .article-cta h3 {
            color: white;
            margin-bottom: 1rem;
        }
        
        .sidebar-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }
        
        .sidebar-card .card-header {
            background: var(--primary);
            color: white;
            padding: 1rem 1.5rem;
            font-weight: 600;
        }
        
        .related-article {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #eee;
            text-decoration: none;
            color: inherit;
            transition: background 0.3s ease;
        }
        
        .related-article:hover {
            background: var(--light);
            color: inherit;
        }
        
        .related-article:last-child {
            border-bottom: none;
        }
        
        .related-article img {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 8px;
            margin-right: 1rem;
        }
        
        .related-article-content h6 {
            margin: 0 0 0.25rem 0;
            font-size: 0.9rem;
            line-height: 1.3;
        }
        
        .related-article-content small {
            color: var(--secondary);
        }

        /* Chat widget */
        .chat-widget {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }

        .chat-button {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: var(--primary);
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .chat-button i {
            font-size: 24px;
        }

        .chat-container {
            position: absolute;
            bottom: 70px;
            right: 0;
            width: 350px;
            height: 450px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 30px rgba(0,0,0,0.1);
            overflow: hidden;
            display: none;
            flex-direction: column;
        }

        .chat-container.active {
            display: flex;
        }

        .chat-header {
            background-color: var(--primary);
            color: white;
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-body {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
            max-height: 320px;
        }

        .chat-message {
            margin-bottom: 15px;
            display: flex;
        }

        .chat-message.incoming {
            justify-content: flex-start;
        }

        .chat-message.outgoing {
            justify-content: flex-end;
        }

        .chat-bubble {
            max-width: 70%;
            padding: 10px 15px;
            border-radius: 20px;
        }

        .chat-message.incoming .chat-bubble {
            background-color: #f0f0f0;
        }

        .chat-message.outgoing .chat-bubble {
            background-color: var(--primary);
            color: white;
        }

        .chat-input {
            display: flex;
            padding: 15px;
            border-top: 1px solid #f0f0f0;
            background-color: white;
            border-radius: 0 0 10px 10px;
            flex-shrink: 0;
        }

        .chat-input input {
            flex-grow: 1;
            border: 1px solid #e0e0e0;
            outline: none;
            padding: 12px 15px;
            border-radius: 25px;
            background-color: #f8f9fa;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .chat-input input:focus {
            border-color: var(--primary);
            background-color: white;
            box-shadow: 0 0 0 2px rgba(0, 86, 179, 0.1);
        }

        .chat-input button {
            background-color: var(--primary);
            color: white;
            border: none;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            margin-left: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            flex-shrink: 0;
        }

        .chat-input button:hover {
            background-color: #004494;
            transform: scale(1.05);
        }

        /* Chat option buttons */
        .chat-bubble .btn {
            font-size: 0.8rem;
            padding: 5px 10px;
            border-radius: 15px;
            transition: all 0.3s ease;
        }

        .chat-bubble .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        /* Fixed Action Buttons */
        .fixed-action-btn {
            position: fixed;
            bottom: 100px;
            right: 20px;
            z-index: 1000;
        }

        .fixed-action-btn .btn {
            margin-bottom: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
        }

        .fixed-action-btn .btn:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
        }

        /* WhatsApp Button Styling */
        .btn-whatsapp {
            background-color: #25D366;
            border-color: #25D366;
            color: white;
        }

        .btn-whatsapp:hover {
            background-color: #128C7E;
            border-color: #128C7E;
            color: white;
        }

        .scroll-top {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: var(--primary);
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            z-index: 99;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .scroll-top.active {
            opacity: 1;
            visibility: visible;
        }
        
        .breadcrumb {
            background: transparent;
            padding: 1rem 0;
        }
        
        .breadcrumb-item a {
            color: var(--primary);
            text-decoration: none;
        }
        
        .share-buttons {
            margin: 2rem 0;
        }
        
        .share-btn {
            display: inline-block;
            padding: 0.5rem 1rem;
            margin: 0.25rem;
            border-radius: 25px;
            color: white;
            text-decoration: none;
            font-size: 0.9rem;
            transition: transform 0.2s ease;
        }
        
        .share-btn:hover {
            transform: translateY(-2px);
            color: white;
        }
        
        .share-facebook { background: #3b5998; }
        .share-twitter { background: #1da1f2; }
        .share-linkedin { background: #0077b5; }
        .share-whatsapp { background: #25d366; }
        
        footer {
            background: var(--dark);
            color: white;
            padding: 3rem 0 1rem;
            margin-top: 4rem;
        }
        
        .footer-section h5 {
            color: var(--accent);
            margin-bottom: 1rem;
        }
        
        .footer-section ul {
            list-style: none;
            padding: 0;
        }
        
        .footer-section ul li {
            margin-bottom: 0.5rem;
        }
        
        .footer-section ul li a {
            color: #ccc;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .footer-section ul li a:hover {
            color: var(--accent);
        }
        
        .copyright {
            border-top: 1px solid #555;
            padding-top: 1rem;
            margin-top: 2rem;
            text-align: center;
            color: #ccc;
        }
        
        @media (max-width: 768px) {
            .article-hero-content h1 {
                font-size: 2rem;
            }

            .article-hero {
                height: 40vh;
            }

            body {
                padding-top: 60px;
            }

            .chat-container {
                width: 300px;
                height: 400px;
                bottom: 80px;
                right: 10px;
            }

            .chat-body {
                max-height: 280px;
            }

            .fixed-action-btn {
                bottom: 80px;
                right: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-tools me-2"></i>Lisbon Appliances
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php#services">Services</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php#about">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="blog.php">Blog</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php#contact">Contact</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <div class="container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                <li class="breadcrumb-item"><a href="blog.php">Blog</a></li>
                <?php if ($article['category_name']): ?>
                    <li class="breadcrumb-item"><?php echo htmlspecialchars($article['category_name']); ?></li>
                <?php endif; ?>
                <li class="breadcrumb-item active" aria-current="page"><?php echo htmlspecialchars($article['title']); ?></li>
            </ol>
        </nav>
    </div>

    <!-- Article Hero -->
    <header class="article-hero">
        <div class="container">
            <div class="article-hero-content">
                <h1><?php echo htmlspecialchars($article['title']); ?></h1>
                <div class="article-meta">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <i class="fas fa-user me-2"></i>By <?php echo htmlspecialchars($article['author_name']); ?>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <i class="fas fa-calendar me-2"></i><?php echo date('F j, Y', strtotime($article['publish_date'])); ?>
                        </div>
                    </div>
                    <?php if ($article['category_name']): ?>
                        <div class="text-center mt-2">
                            <span class="badge bg-warning text-dark">
                                <i class="fas fa-tag me-1"></i><?php echo htmlspecialchars($article['category_name']); ?>
                            </span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </header>

    <!-- Article Content -->
    <div class="container py-5">
                <article class="article-content">
                    <?php if ($article['excerpt']): ?>
                        <p class="lead"><?php echo htmlspecialchars($article['excerpt']); ?></p>
                    <?php endif; ?>
                    
                    <?php echo $article['content']; ?>
                    
                    <!-- Share Buttons -->
                    <div class="share-buttons">
                        <h5>Share this article:</h5>
                        <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode('https://lisbonappliances.com/article.php?slug=' . $article['slug']); ?>" 
                           target="_blank" class="share-btn share-facebook">
                            <i class="fab fa-facebook-f me-1"></i>Facebook
                        </a>
                        <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode('https://lisbonappliances.com/article.php?slug=' . $article['slug']); ?>&text=<?php echo urlencode($article['title']); ?>" 
                           target="_blank" class="share-btn share-twitter">
                            <i class="fab fa-twitter me-1"></i>Twitter
                        </a>
                        <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo urlencode('https://lisbonappliances.com/article.php?slug=' . $article['slug']); ?>" 
                           target="_blank" class="share-btn share-linkedin">
                            <i class="fab fa-linkedin-in me-1"></i>LinkedIn
                        </a>
                        <a href="https://wa.me/?text=<?php echo urlencode($article['title'] . ' - https://lisbonappliances.com/article.php?slug=' . $article['slug']); ?>" 
                           target="_blank" class="share-btn share-whatsapp">
                            <i class="fab fa-whatsapp me-1"></i>WhatsApp
                        </a>
                    </div>
                    
                    <div class="article-cta text-center">
                        <h3>Need Professional Appliance Repair?</h3>
                        <p>Don't let appliance issues disrupt your daily routine. Our expert technicians are ready to help!</p>
                        <a href="index.php#appointment" class="btn btn-light btn-lg">
                            <i class="fas fa-calendar-check me-2"></i>Schedule a Repair
                        </a>
                    </div>
                </article>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="row">
                <div class="col-lg-4 footer-section">
                    <h5>Lisbon Appliances</h5>
                    <p>Professional appliance repair services in Lisbon since 2010. We specialize in refrigerator, washing machine, and cold room repairs with fast response times.</p>
                    <div class="social-links">
                        <a href="#" class="text-light me-3"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 footer-section">
                    <h5>Services</h5>
                    <ul>
                        <li><a href="index.php#services">Refrigerator Repair</a></li>
                        <li><a href="index.php#services">Washing Machine</a></li>
                        <li><a href="index.php#services">Cold Room Service</a></li>
                        <li><a href="index.php#services">Maintenance</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 footer-section">
                    <h5>Company</h5>
                    <ul>
                        <li><a href="index.php#about">About Us</a></li>
                        <li><a href="blog.php">Blog</a></li>
                        <li><a href="index.php#contact">Contact</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                    </ul>
                </div>
                <div class="col-lg-4 footer-section">
                    <h5>Contact Info</h5>
                    <p><i class="fas fa-map-marker-alt me-2"></i>44 Tortelduif Drive, Norkem Park, Kempton Park</p>
                    <p><i class="fas fa-phone me-2"></i>************</p>
                    <p><i class="fas fa-envelope me-2"></i><EMAIL></p>
                    <p><i class="fas fa-clock me-2"></i>Mon-Fri: 8AM-6PM, Sat: 8AM-4PM</p>
                </div>
            </div>
            <div class="copyright">
                <p>&copy; <span id="currentYear"></span> Lisbon Appliances. All Rights Reserved.</p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('currentYear').textContent = new Date().getFullYear();

        // Scroll to top functionality
        window.addEventListener('scroll', function() {
            const scrollTop = document.querySelector('.scroll-top');
            if (window.pageYOffset > 300) {
                scrollTop.classList.add('active');
            } else {
                scrollTop.classList.remove('active');
            }
        });

        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // Chat widget functionality
        function toggleChat() {
            const chatContainer = document.getElementById('chatContainer');
            chatContainer.classList.toggle('active');
        }

        function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();

            if (message) {
                const chatBody = document.getElementById('chatBody');

                // Add user message
                const userMessage = document.createElement('div');
                userMessage.className = 'chat-message outgoing';
                userMessage.innerHTML = `<div class="chat-bubble">${message}</div>`;
                chatBody.appendChild(userMessage);

                // Clear input
                input.value = '';

                // Auto-response
                setTimeout(() => {
                    const botMessage = document.createElement('div');
                    botMessage.className = 'chat-message incoming';
                    botMessage.innerHTML = `<div class="chat-bubble">Thank you for your message! For immediate assistance, please call us at <a href="tel:0691437683">************</a> or <a href="https://wa.me/27691437683" target="_blank">WhatsApp us</a>. We'll get back to you as soon as possible.</div>`;
                    chatBody.appendChild(botMessage);
                    chatBody.scrollTop = chatBody.scrollHeight;
                }, 1000);

                chatBody.scrollTop = chatBody.scrollHeight;
            }
        }

        function handleChatKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // Chatbot option functions
        function requestContactDetails() {
            const chatBody = document.getElementById('chatBody');

            // Add user selection message
            const userMessage = document.createElement('div');
            userMessage.className = 'chat-message outgoing';
            userMessage.innerHTML = `<div class="chat-bubble">📞 Get Contact Details</div>`;
            chatBody.appendChild(userMessage);

            // Bot response with contact details
            setTimeout(() => {
                const botMessage = document.createElement('div');
                botMessage.className = 'chat-message incoming';
                botMessage.innerHTML = `
                    <div class="chat-bubble">
                        Here are our contact details:<br><br>
                        📞 <strong>Phone:</strong> <a href="tel:0691437683" style="color: #0056b3;">************</a><br>
                        💬 <strong>WhatsApp:</strong> <a href="https://wa.me/27691437683" target="_blank" style="color: #25D366;">************</a><br>
                        ✉️ <strong>Email:</strong> <a href="mailto:<EMAIL>" style="color: #0056b3;"><EMAIL></a><br>
                        📍 <strong>Address:</strong> 44 Tortelduif Drive, Norkem Park, Kempton Park, 1618<br><br>
                        Feel free to contact us anytime! 😊
                    </div>
                `;
                chatBody.appendChild(botMessage);
                chatBody.scrollTop = chatBody.scrollHeight;
            }, 1000);

            chatBody.scrollTop = chatBody.scrollHeight;
        }

        function scheduleAppointment() {
            const chatBody = document.getElementById('chatBody');

            // Add user selection message
            const userMessage = document.createElement('div');
            userMessage.className = 'chat-message outgoing';
            userMessage.innerHTML = `<div class="chat-bubble">📅 Schedule Repair</div>`;
            chatBody.appendChild(userMessage);

            // Bot response with appointment link
            setTimeout(() => {
                const botMessage = document.createElement('div');
                botMessage.className = 'chat-message incoming';
                botMessage.innerHTML = `
                    <div class="chat-bubble">
                        Great! I'll help you schedule a repair appointment. 📅<br><br>
                        Please visit our homepage to fill out our appointment form:<br><br>
                        <a href="index.php#appointment" style="background-color: #0056b3; color: white; padding: 10px 15px; border-radius: 5px; text-decoration: none; display: inline-block;">
                            📝 Schedule Your Repair
                        </a><br><br>
                        Or you can call us directly at <a href="tel:0691437683" style="color: #0056b3;">************</a> for immediate assistance! 📞
                    </div>
                `;
                chatBody.appendChild(botMessage);
                chatBody.scrollTop = chatBody.scrollHeight;
            }, 1000);

            chatBody.scrollTop = chatBody.scrollHeight;
        }
    </script>

    <!-- Scroll to Top Button -->
    <div class="scroll-top" onclick="scrollToTop()">
        <i class="fas fa-chevron-up"></i>
    </div>

    <!-- Chat Widget -->
    <div class="chat-widget">
        <div class="chat-button" onclick="toggleChat()">
            <i class="fas fa-comments"></i>
        </div>
        <div class="chat-container" id="chatContainer">
            <div class="chat-header">
                <span>Chat with us</span>
                <i class="fas fa-times" onclick="toggleChat()"></i>
            </div>
            <div class="chat-body" id="chatBody">
                <div class="chat-message incoming">
                    <div class="chat-bubble">
                        👋 Hello! Welcome to Lisbon Appliances! How can we help you today?
                    </div>
                </div>
                <div class="chat-message incoming">
                    <div class="chat-bubble">
                        Please select one of the options below:
                        <div class="mt-2">
                            <button class="btn btn-sm btn-primary me-2 mb-1" onclick="requestContactDetails()">📞 Get Contact Details</button>
                            <button class="btn btn-sm btn-success mb-1" onclick="scheduleAppointment()">📅 Schedule Repair</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="chat-input">
                <input type="text" placeholder="Type your message..." id="chatInput" onkeypress="handleChatKeyPress(event)">
                <button onclick="sendMessage()"><i class="fas fa-paper-plane"></i></button>
            </div>
        </div>
    </div>

    <!-- Fixed Action Buttons -->
    <div class="fixed-action-btn">
        <div>
            <a href="index.php#appointment" class="btn btn-primary rounded-circle" style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;" title="Schedule Repair">
                <i class="fas fa-calendar-alt"></i>
            </a>
        </div>
        <div>
            <a href="tel:0691437683" class="btn btn-primary rounded-circle" style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;" title="Call Now">
                <i class="fas fa-phone"></i>
            </a>
        </div>
        <div>
            <a href="https://wa.me/27691437683" target="_blank" class="btn btn-whatsapp rounded-circle" style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;" title="WhatsApp">
                <i class="fab fa-whatsapp"></i>
            </a>
        </div>
    </div>

</body>
</html>



