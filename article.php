<?php
require_once 'includes/db_connect.php';

// Get article slug from URL
$slug = isset($_GET['slug']) ? $conn->real_escape_string($_GET['slug']) : '';

if (empty($slug)) {
    header("Location: index.php");
    exit;
}

// Get article data
$article_sql = "SELECT p.*, c.name as category_name, u.username as author_name 
                FROM blog_posts p 
                LEFT JOIN categories c ON p.category_id = c.id 
                LEFT JOIN users u ON p.author_id = u.id 
                WHERE p.slug = '$slug' AND p.status = 'published'";
$article_result = $conn->query($article_sql);

if ($article_result->num_rows == 0) {
    header("HTTP/1.0 404 Not Found");
    include '404.php';
    exit;
}

$article = $article_result->fetch_assoc();

// Get related articles
$related_sql = "SELECT title, slug, excerpt, image_url, publish_date 
                FROM blog_posts 
                WHERE category_id = {$article['category_id']} 
                AND id != {$article['id']} 
                AND status = 'published' 
                ORDER BY publish_date DESC 
                LIMIT 3";
$related_result = $conn->query($related_sql);
$related_articles = [];
if ($related_result->num_rows > 0) {
    while($row = $related_result->fetch_assoc()) {
        $related_articles[] = $row;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($article['title']); ?> - Lisbon Appliances</title>
    <meta name="description" content="<?php echo htmlspecialchars($article['excerpt'] ?: substr(strip_tags($article['content']), 0, 160)); ?>">
    <meta name="keywords" content="appliance repair, Lisbon, <?php echo htmlspecialchars($article['category_name']); ?>">
    
    <!-- Open Graph -->
    <meta property="og:title" content="<?php echo htmlspecialchars($article['title']); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars($article['excerpt'] ?: substr(strip_tags($article['content']), 0, 160)); ?>">
    <meta property="og:image" content="<?php echo htmlspecialchars($article['image_url'] ?: 'https://lisbonappliances.com/default-og-image.jpg'); ?>">
    <meta property="og:url" content="https://lisbonappliances.com/article.php?slug=<?php echo htmlspecialchars($article['slug']); ?>">
    <meta property="og:type" content="article">
    
    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo htmlspecialchars($article['title']); ?>">
    <meta name="twitter:description" content="<?php echo htmlspecialchars($article['excerpt'] ?: substr(strip_tags($article['content']), 0, 160)); ?>">
    <meta name="twitter:image" content="<?php echo htmlspecialchars($article['image_url'] ?: 'https://lisbonappliances.com/default-og-image.jpg'); ?>">
    
    <link rel="canonical" href="https://lisbonappliances.com/article.php?slug=<?php echo htmlspecialchars($article['slug']); ?>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    
    <style>
        :root {
            --primary: #0056b3;
            --secondary: #6c757d;
            --accent: #ffc107;
            --light: #f8f9fa;
            --dark: #343a40;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            color: #333;
            line-height: 1.6;
            overflow-x: hidden;
            padding-top: 70px;
        }
        
        .navbar {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 15px 0;
        }
        
        .navbar-brand {
            font-weight: bold;
            color: var(--primary) !important;
        }
        
        .nav-link {
            color: #333 !important;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .nav-link:hover {
            color: var(--primary) !important;
        }
        
        .article-hero {
            background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('<?php echo htmlspecialchars($article['image_url'] ?: 'default-article-bg.jpg'); ?>') center/cover no-repeat;
            height: 60vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            position: relative;
        }
        
        .article-hero-content h1 {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .article-meta {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            padding: 1rem;
            border-radius: 10px;
            margin-top: 2rem;
        }
        
        .article-content {
            font-size: 1.1rem;
            line-height: 1.8;
        }
        
        .article-content h2 {
            color: var(--primary);
            margin-top: 2rem;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        
        .article-content h3 {
            color: var(--dark);
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
        }
        
        .article-content ul, .article-content ol {
            margin: 1rem 0;
            padding-left: 2rem;
        }
        
        .article-content li {
            margin-bottom: 0.5rem;
        }
        
        .article-content blockquote {
            border-left: 4px solid var(--primary);
            background: var(--light);
            padding: 1rem 1.5rem;
            margin: 1.5rem 0;
            font-style: italic;
        }
        
        .article-cta {
            background: linear-gradient(135deg, var(--primary), #004494);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin: 3rem 0;
        }
        
        .article-cta h3 {
            color: white;
            margin-bottom: 1rem;
        }
        
        .sidebar-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }
        
        .sidebar-card .card-header {
            background: var(--primary);
            color: white;
            padding: 1rem 1.5rem;
            font-weight: 600;
        }
        
        .related-article {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #eee;
            text-decoration: none;
            color: inherit;
            transition: background 0.3s ease;
        }
        
        .related-article:hover {
            background: var(--light);
            color: inherit;
        }
        
        .related-article:last-child {
            border-bottom: none;
        }
        
        .related-article img {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 8px;
            margin-right: 1rem;
        }
        
        .related-article-content h6 {
            margin: 0 0 0.25rem 0;
            font-size: 0.9rem;
            line-height: 1.3;
        }
        
        .related-article-content small {
            color: var(--secondary);
        }
        
        .breadcrumb {
            background: transparent;
            padding: 1rem 0;
        }
        
        .breadcrumb-item a {
            color: var(--primary);
            text-decoration: none;
        }
        
        .share-buttons {
            margin: 2rem 0;
        }
        
        .share-btn {
            display: inline-block;
            padding: 0.5rem 1rem;
            margin: 0.25rem;
            border-radius: 25px;
            color: white;
            text-decoration: none;
            font-size: 0.9rem;
            transition: transform 0.2s ease;
        }
        
        .share-btn:hover {
            transform: translateY(-2px);
            color: white;
        }
        
        .share-facebook { background: #3b5998; }
        .share-twitter { background: #1da1f2; }
        .share-linkedin { background: #0077b5; }
        .share-whatsapp { background: #25d366; }
        
        footer {
            background: var(--dark);
            color: white;
            padding: 3rem 0 1rem;
            margin-top: 4rem;
        }
        
        .footer-section h5 {
            color: var(--accent);
            margin-bottom: 1rem;
        }
        
        .footer-section ul {
            list-style: none;
            padding: 0;
        }
        
        .footer-section ul li {
            margin-bottom: 0.5rem;
        }
        
        .footer-section ul li a {
            color: #ccc;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .footer-section ul li a:hover {
            color: var(--accent);
        }
        
        .copyright {
            border-top: 1px solid #555;
            padding-top: 1rem;
            margin-top: 2rem;
            text-align: center;
            color: #ccc;
        }
        
        @media (max-width: 768px) {
            .article-hero-content h1 {
                font-size: 2rem;
            }
            
            .article-hero {
                height: 40vh;
            }
            
            body {
                padding-top: 60px;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-tools me-2"></i>Lisbon Appliances
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php#services">Services</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php#about">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="blog.php">Blog</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php#contact">Contact</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <div class="container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                <li class="breadcrumb-item"><a href="blog.php">Blog</a></li>
                <?php if ($article['category_name']): ?>
                    <li class="breadcrumb-item"><?php echo htmlspecialchars($article['category_name']); ?></li>
                <?php endif; ?>
                <li class="breadcrumb-item active" aria-current="page"><?php echo htmlspecialchars($article['title']); ?></li>
            </ol>
        </nav>
    </div>

    <!-- Article Hero -->
    <header class="article-hero">
        <div class="container">
            <div class="article-hero-content">
                <h1><?php echo htmlspecialchars($article['title']); ?></h1>
                <div class="article-meta">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <i class="fas fa-user me-2"></i>By <?php echo htmlspecialchars($article['author_name']); ?>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <i class="fas fa-calendar me-2"></i><?php echo date('F j, Y', strtotime($article['publish_date'])); ?>
                        </div>
                    </div>
                    <?php if ($article['category_name']): ?>
                        <div class="text-center mt-2">
                            <span class="badge bg-warning text-dark">
                                <i class="fas fa-tag me-1"></i><?php echo htmlspecialchars($article['category_name']); ?>
                            </span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </header>

    <!-- Article Content -->
    <div class="container py-5">
        <div class="row">
            <div class="col-lg-8">
                <article class="article-content">
                    <?php if ($article['excerpt']): ?>
                        <p class="lead"><?php echo htmlspecialchars($article['excerpt']); ?></p>
                    <?php endif; ?>
                    
                    <?php echo $article['content']; ?>
                    
                    <!-- Share Buttons -->
                    <div class="share-buttons">
                        <h5>Share this article:</h5>
                        <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode('https://lisbonappliances.com/article.php?slug=' . $article['slug']); ?>" 
                           target="_blank" class="share-btn share-facebook">
                            <i class="fab fa-facebook-f me-1"></i>Facebook
                        </a>
                        <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode('https://lisbonappliances.com/article.php?slug=' . $article['slug']); ?>&text=<?php echo urlencode($article['title']); ?>" 
                           target="_blank" class="share-btn share-twitter">
                            <i class="fab fa-twitter me-1"></i>Twitter
                        </a>
                        <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo urlencode('https://lisbonappliances.com/article.php?slug=' . $article['slug']); ?>" 
                           target="_blank" class="share-btn share-linkedin">
                            <i class="fab fa-linkedin-in me-1"></i>LinkedIn
                        </a>
                        <a href="https://wa.me/?text=<?php echo urlencode($article['title'] . ' - https://lisbonappliances.com/article.php?slug=' . $article['slug']); ?>" 
                           target="_blank" class="share-btn share-whatsapp">
                            <i class="fab fa-whatsapp me-1"></i>WhatsApp
                        </a>
                    </div>
                    
                    <div class="article-cta text-center">
                        <h3>Need Professional Appliance Repair?</h3>
                        <p>Don't let appliance issues disrupt your daily routine. Our expert technicians are ready to help!</p>
                        <a href="index.php#appointment" class="btn btn-light btn-lg">
                            <i class="fas fa-calendar-check me-2"></i>Schedule a Repair
                        </a>
                    </div>
                </article>
            </div>
            
            <aside class="col-lg-4">
                <div class="sticky-top" style="top: 100px;">
                    <!-- About Lisbon Appliances -->
                    <div class="sidebar-card">
                        <div class="card-header">
                            <i class="fas fa-info-circle me-2"></i>About Lisbon Appliances
                        </div>
                        <div class="card-body">
                            <p>Your trusted partner for professional appliance repair services in Lisbon since 2010. We specialize in refrigerator, washing machine, and cold room repairs.</p>
                            <a href="index.php#about" class="btn btn-outline-primary btn-sm">Learn More</a>
                        </div>
                    </div>
                    
                    <!-- Related Articles -->
                    <?php if (!empty($related_articles)): ?>
                        <div class="sidebar-card">
                            <div class="card-header">
                                <i class="fas fa-newspaper me-2"></i>Related Articles
                            </div>
                            <div class="card-body p-0">
                                <?php foreach ($related_articles as $related): ?>
                                    <a href="article.php?slug=<?php echo htmlspecialchars($related['slug']); ?>" class="related-article">
                                        <?php if ($related['image_url']): ?>
                                            <img src="<?php echo htmlspecialchars($related['image_url']); ?>" alt="<?php echo htmlspecialchars($related['title']); ?>">
                                        <?php else: ?>
                                            <div style="width: 60px; height: 60px; background: var(--light); border-radius: 8px; margin-right: 1rem; display: flex; align-items: center; justify-content: center;">
                                                <i class="fas fa-file-alt text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                        <div class="related-article-content">
                                            <h6><?php echo htmlspecialchars($related['title']); ?></h6>
                                            <small><?php echo date('M j, Y', strtotime($related['publish_date'])); ?></small>
                                        </div>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Contact CTA -->
                    <div class="sidebar-card">
                        <div class="card-header">
                            <i class="fas fa-phone me-2"></i>Need Help Now?
                        </div>
                        <div class="card-body text-center">
                            <p>Get immediate assistance from our expert technicians.</p>
                            <a href="tel:0691437683" class="btn btn-primary mb-2">
                                <i class="fas fa-phone me-1"></i>************
                            </a>
                            <br>
                            <a href="https://wa.me/27691437683" target="_blank" class="btn btn-success">
                                <i class="fab fa-whatsapp me-1"></i>WhatsApp
                            </a>
                        </div>
                    </div>
                </div>
            </aside>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="row">
                <div class="col-lg-4 footer-section">
                    <h5>Lisbon Appliances</h5>
                    <p>Professional appliance repair services in Lisbon since 2010. We specialize in refrigerator, washing machine, and cold room repairs with fast response times.</p>
                    <div class="social-links">
                        <a href="#" class="text-light me-3"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 footer-section">
                    <h5>Services</h5>
                    <ul>
                        <li><a href="index.php#services">Refrigerator Repair</a></li>
                        <li><a href="index.php#services">Washing Machine</a></li>
                        <li><a href="index.php#services">Cold Room Service</a></li>
                        <li><a href="index.php#services">Maintenance</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 footer-section">
                    <h5>Company</h5>
                    <ul>
                        <li><a href="index.php#about">About Us</a></li>
                        <li><a href="blog.php">Blog</a></li>
                        <li><a href="index.php#contact">Contact</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                    </ul>
                </div>
                <div class="col-lg-4 footer-section">
                    <h5>Contact Info</h5>
                    <p><i class="fas fa-map-marker-alt me-2"></i>44 Tortelduif Drive, Norkem Park, Kempton Park</p>
                    <p><i class="fas fa-phone me-2"></i>************</p>
                    <p><i class="fas fa-envelope me-2"></i><EMAIL></p>
                    <p><i class="fas fa-clock me-2"></i>Mon-Fri: 8AM-6PM, Sat: 8AM-4PM</p>
                </div>
            </div>
            <div class="copyright">
                <p>&copy; <span id="currentYear"></span> Lisbon Appliances. All Rights Reserved.</p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('currentYear').textContent = new Date().getFullYear();
    </script>
</body>
</html>



