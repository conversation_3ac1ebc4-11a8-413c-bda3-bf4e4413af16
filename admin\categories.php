<?php
session_start();
require_once '../includes/db_connect.php';
require_once 'includes/auth_check.php';

$success = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $name = $conn->real_escape_string($_POST['name']);
                $description = $conn->real_escape_string($_POST['description']);
                
                $sql = "INSERT INTO categories (name, description, created_at) VALUES ('$name', '$description', NOW())";
                if ($conn->query($sql)) {
                    $success = 'Category added successfully!';
                } else {
                    $error = 'Error adding category: ' . $conn->error;
                }
                break;
                
            case 'edit':
                $id = (int)$_POST['id'];
                $name = $conn->real_escape_string($_POST['name']);
                $description = $conn->real_escape_string($_POST['description']);
                
                $sql = "UPDATE categories SET name = '$name', description = '$description' WHERE id = $id";
                if ($conn->query($sql)) {
                    $success = 'Category updated successfully!';
                } else {
                    $error = 'Error updating category: ' . $conn->error;
                }
                break;
        }
    }
}

// Handle delete
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $id = (int)$_GET['delete'];
    
    // Check if category has posts
    $check_sql = "SELECT COUNT(*) as count FROM blog_posts WHERE category_id = $id";
    $check_result = $conn->query($check_sql);
    $post_count = $check_result->fetch_assoc()['count'];
    
    if ($post_count > 0) {
        $error = "Cannot delete category. It has $post_count post(s) assigned to it.";
    } else {
        $delete_sql = "DELETE FROM categories WHERE id = $id";
        if ($conn->query($delete_sql)) {
            $success = 'Category deleted successfully!';
        } else {
            $error = 'Error deleting category: ' . $conn->error;
        }
    }
}

// Get categories with post counts
$categories_sql = "SELECT c.*, COUNT(p.id) as post_count 
                   FROM categories c 
                   LEFT JOIN blog_posts p ON c.id = p.category_id 
                   GROUP BY c.id 
                   ORDER BY c.name";
$categories_result = $conn->query($categories_sql);

// Get category for editing
$edit_category = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $edit_id = (int)$_GET['edit'];
    $edit_sql = "SELECT * FROM categories WHERE id = $edit_id";
    $edit_result = $conn->query($edit_sql);
    if ($edit_result->num_rows > 0) {
        $edit_category = $edit_result->fetch_assoc();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Categories - Lisbon Appliances Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/admin.css">
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Categories</h1>
                </div>

                <?php if ($success): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <?php echo $edit_category ? 'Edit Category' : 'Add New Category'; ?>
                                </h6>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <input type="hidden" name="action" value="<?php echo $edit_category ? 'edit' : 'add'; ?>">
                                    <?php if ($edit_category): ?>
                                        <input type="hidden" name="id" value="<?php echo $edit_category['id']; ?>">
                                    <?php endif; ?>
                                    
                                    <div class="mb-3">
                                        <label for="name" class="form-label">Name</label>
                                        <input type="text" class="form-control" id="name" name="name" required
                                               value="<?php echo $edit_category ? htmlspecialchars($edit_category['name']) : ''; ?>"
                                               placeholder="Category name">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="description" class="form-label">Description</label>
                                        <textarea class="form-control" id="description" name="description" rows="3"
                                                  placeholder="Optional description"><?php echo $edit_category ? htmlspecialchars($edit_category['description']) : ''; ?></textarea>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i>
                                            <?php echo $edit_category ? 'Update Category' : 'Add Category'; ?>
                                        </button>
                                        <?php if ($edit_category): ?>
                                            <a href="categories.php" class="btn btn-outline-secondary">
                                                <i class="fas fa-times me-1"></i>Cancel
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">All Categories</h6>
                            </div>
                            <div class="card-body">
                                <?php if ($categories_result->num_rows > 0): ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Name</th>
                                                    <th>Description</th>
                                                    <th>Posts</th>
                                                    <th>Created</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php while($category = $categories_result->fetch_assoc()): ?>
                                                <tr>
                                                    <td>
                                                        <strong><?php echo htmlspecialchars($category['name']); ?></strong>
                                                    </td>
                                                    <td>
                                                        <?php echo $category['description'] ? htmlspecialchars($category['description']) : '<span class="text-muted">No description</span>'; ?>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-secondary"><?php echo $category['post_count']; ?></span>
                                                    </td>
                                                    <td>
                                                        <?php echo date('M j, Y', strtotime($category['created_at'])); ?>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <a href="categories.php?edit=<?php echo $category['id']; ?>" 
                                                               class="btn btn-sm btn-outline-secondary" title="Edit">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <?php if ($category['post_count'] == 0): ?>
                                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                                        onclick="deleteCategory(<?php echo $category['id']; ?>, '<?php echo htmlspecialchars($category['name']); ?>')" 
                                                                        title="Delete">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            <?php else: ?>
                                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                                        disabled title="Cannot delete - has posts">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <?php endwhile; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <div class="text-center py-5">
                                        <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">No categories found</h5>
                                        <p class="text-muted">Create your first category to organize your posts!</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function deleteCategory(id, name) {
            if (confirm(`Are you sure you want to delete the category "${name}"? This action cannot be undone.`)) {
                window.location.href = `categories.php?delete=${id}`;
            }
        }
    </script>
</body>
</html>
