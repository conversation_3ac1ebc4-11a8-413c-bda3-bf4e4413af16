<?php
require_once 'includes/db_connect.php';

// Pagination settings
$posts_per_page = 9;
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$offset = ($page - 1) * $posts_per_page;

// Category filter
$category_filter = '';
$selected_category = '';
if (isset($_GET['category']) && !empty($_GET['category'])) {
    $selected_category = $conn->real_escape_string($_GET['category']);
    $category_filter = "AND c.name = '$selected_category'";
}

// Search filter
$search_filter = '';
$search_query = '';
if (isset($_GET['search']) && !empty($_GET['search'])) {
    $search_query = $conn->real_escape_string($_GET['search']);
    $search_filter = "AND (p.title LIKE '%$search_query%' OR p.excerpt LIKE '%$search_query%' OR p.content LIKE '%$search_query%')";
}

// Get total posts count
$count_sql = "SELECT COUNT(*) as total 
              FROM blog_posts p 
              LEFT JOIN categories c ON p.category_id = c.id 
              WHERE p.status = 'published' $category_filter $search_filter";
$count_result = $conn->query($count_sql);
$total_posts = $count_result->fetch_assoc()['total'];
$total_pages = ceil($total_posts / $posts_per_page);

// Get posts
$posts_sql = "SELECT p.*, c.name as category_name, u.username as author_name 
              FROM blog_posts p 
              LEFT JOIN categories c ON p.category_id = c.id 
              LEFT JOIN users u ON p.author_id = u.id 
              WHERE p.status = 'published' $category_filter $search_filter
              ORDER BY p.publish_date DESC 
              LIMIT $posts_per_page OFFSET $offset";
$posts_result = $conn->query($posts_sql);

// Get categories for filter
$categories_sql = "SELECT c.name, COUNT(p.id) as post_count 
                   FROM categories c 
                   LEFT JOIN blog_posts p ON c.id = p.category_id AND p.status = 'published'
                   GROUP BY c.id, c.name 
                   HAVING post_count > 0
                   ORDER BY c.name";
$categories_result = $conn->query($categories_sql);
$categories = [];
if ($categories_result->num_rows > 0) {
    while($row = $categories_result->fetch_assoc()) {
        $categories[] = $row;
    }
}


?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blog - Appliance Repair Tips & Guides | Lisbon Appliances</title>
    <meta name="description" content="Expert appliance repair tips, maintenance guides, and industry insights from Lisbon Appliances. Learn how to keep your appliances running efficiently.">
    <meta name="keywords" content="appliance repair tips, maintenance guides, refrigerator repair, washing machine repair, cold room maintenance, Lisbon">
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    
    <style>
        :root {
            --primary: #0056b3;
            --secondary: #6c757d;
            --accent: #ffc107;
            --light: #f8f9fa;
            --dark: #343a40;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            color: #333;
            line-height: 1.6;
            overflow-x: hidden;
            padding-top: 70px;
        }
        
        .navbar {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 15px 0;
        }
        
        .navbar-brand {
            font-weight: bold;
            color: var(--primary) !important;
        }
        
        .nav-link {
            color: #333 !important;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .nav-link:hover, .nav-link.active {
            color: var(--primary) !important;
        }
        
        .blog-hero {
            background: linear-gradient(135deg, var(--primary), #004494);
            color: white;
            padding: 4rem 0;
            text-align: center;
        }
        
        .blog-hero h1 {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        
        .blog-hero p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .search-filter-section {
            background: white;
            padding: 2rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .blog-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: all 0.3s ease;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .blog-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }
        
        .blog-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        
        .blog-card-content {
            padding: 1.5rem;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }
        
        .blog-card-content h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            color: var(--dark);
            line-height: 1.4;
        }
        
        .blog-card-content h3 a {
            color: inherit;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .blog-card-content h3 a:hover {
            color: var(--primary);
        }
        
        .blog-card-excerpt {
            color: var(--secondary);
            margin-bottom: 1rem;
            flex-grow: 1;
        }
        
        .blog-card-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
            color: var(--secondary);
            margin-top: auto;
        }
        
        .category-badge {
            background: var(--primary);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            text-decoration: none;
            transition: background 0.3s ease;
        }
        
        .category-badge:hover {
            background: #004494;
            color: white;
        }
        
        .sidebar-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }
        
        .sidebar-card .card-header {
            background: var(--primary);
            color: white;
            padding: 1rem 1.5rem;
            font-weight: 600;
            border: none;
        }
        
        .recent-post {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #eee;
            text-decoration: none;
            color: inherit;
            transition: background 0.3s ease;
        }
        
        .recent-post:hover {
            background: var(--light);
            color: inherit;
        }
        
        .recent-post:last-child {
            border-bottom: none;
        }
        
        .recent-post img {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 8px;
            margin-right: 1rem;
        }
        
        .recent-post-content h6 {
            margin: 0 0 0.25rem 0;
            font-size: 0.9rem;
            line-height: 1.3;
        }
        
        .recent-post-content small {
            color: var(--secondary);
        }
        
        .pagination {
            justify-content: center;
            margin-top: 3rem;
        }
        
        .page-link {
            color: var(--primary);
            border-color: #dee2e6;
        }
        
        .page-link:hover {
            color: #004494;
            background-color: var(--light);
            border-color: #dee2e6;
        }
        
        .page-item.active .page-link {
            background-color: var(--primary);
            border-color: var(--primary);
        }
        
        .no-posts {
            text-align: center;
            padding: 4rem 0;
            color: var(--secondary);
        }
        
        .no-posts i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        
        footer {
            background: var(--dark);
            color: white;
            padding: 3rem 0 1rem;
            margin-top: 4rem;
        }
        
        .footer-section h5 {
            color: var(--accent);
            margin-bottom: 1rem;
        }
        
        .footer-section ul {
            list-style: none;
            padding: 0;
        }
        
        .footer-section ul li {
            margin-bottom: 0.5rem;
        }
        
        .footer-section ul li a {
            color: #ccc;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .footer-section ul li a:hover {
            color: var(--accent);
        }
        
        .copyright {
            border-top: 1px solid #555;
            padding-top: 1rem;
            margin-top: 2rem;
            text-align: center;
            color: #ccc;
        }
        
        @media (max-width: 768px) {
            .blog-hero h1 {
                font-size: 2rem;
            }
            
            body {
                padding-top: 60px;
            }
            
            .search-filter-section {
                padding: 1rem 0;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-tools me-2"></i>Lisbon Appliances
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php#services">Services</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php#about">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="blog.php">Blog</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php#contact">Contact</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Blog Hero -->
    <section class="blog-hero">
        <div class="container">
            <h1>Appliance Repair Blog</h1>
            <p>Expert tips, maintenance guides, and industry insights to keep your appliances running smoothly</p>
        </div>
    </section>

    <!-- Search and Filter -->
    <section class="search-filter-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <form method="GET" class="d-flex">
                        <input type="hidden" name="category" value="<?php echo htmlspecialchars($selected_category); ?>">
                        <input type="text" class="form-control me-2" name="search" 
                               placeholder="Search articles..." 
                               value="<?php echo htmlspecialchars($search_query); ?>">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-md-end mt-3 mt-md-0">
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-filter me-1"></i>
                                <?php echo $selected_category ? htmlspecialchars($selected_category) : 'All Categories'; ?>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="blog.php<?php echo $search_query ? '?search=' . urlencode($search_query) : ''; ?>">All Categories</a></li>
                                <?php foreach ($categories as $category): ?>
                                    <li>
                                        <a class="dropdown-item" href="blog.php?category=<?php echo urlencode($category['name']); ?><?php echo $search_query ? '&search=' . urlencode($search_query) : ''; ?>">
                                            <?php echo htmlspecialchars($category['name']); ?> (<?php echo $category['post_count']; ?>)
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <?php if ($selected_category || $search_query): ?>
                <div class="mt-3">
                    <div class="d-flex align-items-center">
                        <span class="me-2">Active filters:</span>
                        <?php if ($selected_category): ?>
                            <span class="badge bg-primary me-2">
                                Category: <?php echo htmlspecialchars($selected_category); ?>
                                <a href="blog.php<?php echo $search_query ? '?search=' . urlencode($search_query) : ''; ?>" class="text-white ms-1">×</a>
                            </span>
                        <?php endif; ?>
                        <?php if ($search_query): ?>
                            <span class="badge bg-secondary me-2">
                                Search: "<?php echo htmlspecialchars($search_query); ?>"
                                <a href="blog.php<?php echo $selected_category ? '?category=' . urlencode($selected_category) : ''; ?>" class="text-white ms-1">×</a>
                            </span>
                        <?php endif; ?>
                        <a href="blog.php" class="btn btn-sm btn-outline-secondary">Clear All</a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Blog Content -->
    <div class="container py-5">
                <?php if ($posts_result->num_rows > 0): ?>
                    <div class="row">
                        <?php while($post = $posts_result->fetch_assoc()): ?>
                            <div class="col-md-6 mb-4">
                                <article class="blog-card">
                                    <?php if ($post['image_url']): ?>
                                        <img src="<?php echo htmlspecialchars($post['image_url']); ?>" alt="<?php echo htmlspecialchars($post['title']); ?>">
                                    <?php else: ?>
                                        <div style="height: 200px; background: var(--light); display: flex; align-items: center; justify-content: center;">
                                            <i class="fas fa-file-alt fa-3x text-muted"></i>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="blog-card-content">
                                        <h3>
                                            <a href="article.php?slug=<?php echo htmlspecialchars($post['slug']); ?>">
                                                <?php echo htmlspecialchars($post['title']); ?>
                                            </a>
                                        </h3>
                                        
                                        <?php if ($post['excerpt']): ?>
                                            <p class="blog-card-excerpt"><?php echo htmlspecialchars($post['excerpt']); ?></p>
                                        <?php else: ?>
                                            <p class="blog-card-excerpt"><?php echo htmlspecialchars(substr(strip_tags($post['content']), 0, 120)) . '...'; ?></p>
                                        <?php endif; ?>
                                        
                                        <div class="blog-card-meta">
                                            <div>
                                                <?php if ($post['category_name']): ?>
                                                    <a href="blog.php?category=<?php echo urlencode($post['category_name']); ?>" class="category-badge">
                                                        <?php echo htmlspecialchars($post['category_name']); ?>
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                            <small>
                                                <i class="fas fa-calendar me-1"></i>
                                                <?php echo date('M j, Y', strtotime($post['publish_date'])); ?>
                                            </small>
                                        </div>
                                    </div>
                                </article>
                            </div>
                        <?php endwhile; ?>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($total_pages > 1): ?>
                        <nav aria-label="Blog pagination">
                            <ul class="pagination">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="blog.php?page=<?php echo $page - 1; ?><?php echo $selected_category ? '&category=' . urlencode($selected_category) : ''; ?><?php echo $search_query ? '&search=' . urlencode($search_query) : ''; ?>">
                                            <i class="fas fa-chevron-left"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="blog.php?page=<?php echo $i; ?><?php echo $selected_category ? '&category=' . urlencode($selected_category) : ''; ?><?php echo $search_query ? '&search=' . urlencode($search_query) : ''; ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>
                                
                                <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="blog.php?page=<?php echo $page + 1; ?><?php echo $selected_category ? '&category=' . urlencode($selected_category) : ''; ?><?php echo $search_query ? '&search=' . urlencode($search_query) : ''; ?>">
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                    
                <?php else: ?>
                    <div class="no-posts">
                        <i class="fas fa-search"></i>
                        <h3>No articles found</h3>
                        <p>
                            <?php if ($search_query || $selected_category): ?>
                                Try adjusting your search criteria or <a href="blog.php">browse all articles</a>.
                            <?php else: ?>
                                Check back soon for new content!
                            <?php endif; ?>
                        </p>
                    </div>
                <?php endif; ?>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="row">
                <div class="col-lg-4 footer-section">
                    <h5>Lisbon Appliances</h5>
                    <p>Professional appliance repair services in Lisbon since 2010. We specialize in refrigerator, washing machine, and cold room repairs with fast response times.</p>
                    <div class="social-links">
                        <a href="#" class="text-light me-3"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 footer-section">
                    <h5>Services</h5>
                    <ul>
                        <li><a href="index.php#services">Refrigerator Repair</a></li>
                        <li><a href="index.php#services">Washing Machine</a></li>
                        <li><a href="index.php#services">Cold Room Service</a></li>
                        <li><a href="index.php#services">Maintenance</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 footer-section">
                    <h5>Company</h5>
                    <ul>
                        <li><a href="index.php#about">About Us</a></li>
                        <li><a href="blog.php">Blog</a></li>
                        <li><a href="index.php#contact">Contact</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                    </ul>
                </div>
                <div class="col-lg-4 footer-section">
                    <h5>Contact Info</h5>
                    <p><i class="fas fa-map-marker-alt me-2"></i>44 Tortelduif Drive, Norkem Park, Kempton Park</p>
                    <p><i class="fas fa-phone me-2"></i>************</p>
                    <p><i class="fas fa-envelope me-2"></i><EMAIL></p>
                    <p><i class="fas fa-clock me-2"></i>Mon-Fri: 8AM-6PM, Sat: 8AM-4PM</p>
                </div>
            </div>
            <div class="copyright">
                <p>&copy; <span id="currentYear"></span> Lisbon Appliances. All Rights Reserved.</p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('currentYear').textContent = new Date().getFullYear();

        // Newsletter subscription
        document.getElementById('newsletterForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const email = document.getElementById('newsletterEmail').value;
            const btn = document.getElementById('newsletterBtn');
            const message = document.getElementById('newsletterMessage');

            // Disable button and show loading
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Subscribing...';

            fetch('api/newsletter_subscribe.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email: email })
            })
            .then(response => response.json())
            .then(data => {
                message.style.display = 'block';
                if (data.error) {
                    message.className = 'mt-2 alert alert-danger';
                    message.textContent = data.error;
                } else {
                    message.className = 'mt-2 alert alert-success';
                    message.textContent = data.message;
                    document.getElementById('newsletterEmail').value = '';
                }
            })
            .catch(error => {
                message.style.display = 'block';
                message.className = 'mt-2 alert alert-danger';
                message.textContent = 'An error occurred. Please try again.';
            })
            .finally(() => {
                btn.disabled = false;
                btn.innerHTML = 'Subscribe';
            });
        });

        // Add hover effect for category links
        document.addEventListener('DOMContentLoaded', function() {
            const categoryLinks = document.querySelectorAll('.hover-bg-light');
            categoryLinks.forEach(link => {
                link.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = '#f8f9fa';
                });
                link.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = 'transparent';
                });
            });
        });
    </script>
</body>
</html>

