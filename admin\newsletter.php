<?php
session_start();
require_once '../includes/db_connect.php';
require_once 'includes/auth_check.php';

$success = '';
$error = '';

// Handle actions
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] == 'export_subscribers') {
        // Export subscribers to CSV
        $filename = 'newsletter_subscribers_' . date('Y-m-d') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        fputcsv($output, ['Email', 'Name', 'Status', 'Subscribed Date', 'IP Address']);
        
        $export_sql = "SELECT email, name, status, subscribed_at, ip_address FROM newsletter_subscribers ORDER BY subscribed_at DESC";
        $export_result = $conn->query($export_sql);
        
        while ($row = $export_result->fetch_assoc()) {
            fputcsv($output, [
                $row['email'],
                $row['name'],
                $row['status'],
                $row['subscribed_at'],
                $row['ip_address']
            ]);
        }
        
        fclose($output);
        exit;
    } elseif ($_POST['action'] == 'delete_subscriber') {
        $id = (int)$_POST['subscriber_id'];
        $sql = "DELETE FROM newsletter_subscribers WHERE id = $id";
        
        if ($conn->query($sql)) {
            $success = 'Subscriber deleted successfully!';
        } else {
            $error = 'Error deleting subscriber: ' . $conn->error;
        }
    } elseif ($_POST['action'] == 'toggle_status') {
        $id = (int)$_POST['subscriber_id'];
        $new_status = $_POST['new_status'] == 'active' ? 'active' : 'unsubscribed';
        
        $sql = "UPDATE newsletter_subscribers SET status = '$new_status'";
        if ($new_status == 'unsubscribed') {
            $sql .= ", unsubscribed_at = NOW()";
        } else {
            $sql .= ", unsubscribed_at = NULL";
        }
        $sql .= " WHERE id = $id";
        
        if ($conn->query($sql)) {
            $success = 'Subscriber status updated successfully!';
        } else {
            $error = 'Error updating subscriber status: ' . $conn->error;
        }
    }
}

// Get statistics
$stats = [];

$result = $conn->query("SELECT COUNT(*) as total FROM newsletter_subscribers");
$stats['total'] = $result->fetch_assoc()['total'];

$result = $conn->query("SELECT COUNT(*) as total FROM newsletter_subscribers WHERE status = 'active'");
$stats['active'] = $result->fetch_assoc()['total'];

$result = $conn->query("SELECT COUNT(*) as total FROM newsletter_subscribers WHERE status = 'unsubscribed'");
$stats['unsubscribed'] = $result->fetch_assoc()['total'];

$result = $conn->query("SELECT COUNT(*) as total FROM newsletter_subscribers WHERE DATE(subscribed_at) = CURDATE()");
$stats['today'] = $result->fetch_assoc()['total'];

// Get subscribers with pagination
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$per_page = 20;
$offset = ($page - 1) * $per_page;

$subscribers_sql = "SELECT * FROM newsletter_subscribers ORDER BY subscribed_at DESC LIMIT $per_page OFFSET $offset";
$subscribers_result = $conn->query($subscribers_sql);
$subscribers = [];
if ($subscribers_result->num_rows > 0) {
    while($row = $subscribers_result->fetch_assoc()) {
        $subscribers[] = $row;
    }
}

$total_pages = ceil($stats['total'] / $per_page);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Newsletter - Lisbon Appliances Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/admin.css">
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Newsletter Management</h1>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="export_subscribers">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-download me-1"></i>Export CSV
                        </button>
                    </form>
                </div>

                <?php if ($success): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-primary"><?php echo $stats['total']; ?></h5>
                                <p class="card-text">Total Subscribers</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-success"><?php echo $stats['active']; ?></h5>
                                <p class="card-text">Active</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-warning"><?php echo $stats['unsubscribed']; ?></h5>
                                <p class="card-text">Unsubscribed</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-info"><?php echo $stats['today']; ?></h5>
                                <p class="card-text">Today</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Subscribers List -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Subscribers</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($subscribers)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-envelope fa-3x text-muted mb-3"></i>
                                <h5>No subscribers yet</h5>
                                <p class="text-muted">Newsletter subscriptions will appear here.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Email</th>
                                            <th>Name</th>
                                            <th>Status</th>
                                            <th>Subscribed</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($subscribers as $subscriber): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($subscriber['email']); ?></td>
                                                <td><?php echo htmlspecialchars($subscriber['name'] ?: 'N/A'); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php echo $subscriber['status'] == 'active' ? 'success' : 'secondary'; ?>">
                                                        <?php echo ucfirst($subscriber['status']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo date('M j, Y', strtotime($subscriber['subscribed_at'])); ?></td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                                            onclick="toggleStatus(<?php echo $subscriber['id']; ?>, '<?php echo $subscriber['status'] == 'active' ? 'unsubscribed' : 'active'; ?>')">
                                                        <?php echo $subscriber['status'] == 'active' ? 'Unsubscribe' : 'Reactivate'; ?>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                                            onclick="deleteSubscriber(<?php echo $subscriber['id']; ?>, '<?php echo htmlspecialchars($subscriber['email'], ENT_QUOTES); ?>')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <?php if ($total_pages > 1): ?>
                                <nav aria-label="Subscribers pagination">
                                    <ul class="pagination justify-content-center">
                                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                                <a class="page-link" href="newsletter.php?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                            </li>
                                        <?php endfor; ?>
                                    </ul>
                                </nav>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function toggleStatus(id, newStatus) {
            const action = newStatus === 'active' ? 'reactivate' : 'unsubscribe';
            if (confirm(`Are you sure you want to ${action} this subscriber?`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="toggle_status">
                    <input type="hidden" name="subscriber_id" value="${id}">
                    <input type="hidden" name="new_status" value="${newStatus}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function deleteSubscriber(id, email) {
            if (confirm(`Are you sure you want to permanently delete the subscriber "${email}"?`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_subscriber">
                    <input type="hidden" name="subscriber_id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
