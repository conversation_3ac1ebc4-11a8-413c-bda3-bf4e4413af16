<?php
// Database connection
require_once 'includes/db_connect.php';

// Fetch featured blog posts
$sql = "SELECT id, title, excerpt, image_url, publish_date, category FROM blog_posts WHERE featured = 1 ORDER BY publish_date DESC LIMIT 6";
$result = $conn->query($sql);
$featuredPosts = [];

if ($result->num_rows > 0) {
    while($row = $result->fetch_assoc()) {
        $featuredPosts[] = $row;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Keep existing head content -->
</head>
<body>
    <!-- Navigation with Blog link added -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">Lisbon Appliances</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#home">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">About Us</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#services">Services</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#testimonials">Testimonials</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="blog.php">Blog</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#faq">FAQ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">Contact</a>
                    </li>
                </ul>
                <a href="#appointment" class="btn btn-primary ms-3">Schedule Repair</a>
            </div>
        </div>
    </nav>

    <!-- Keep existing content until Blog Section -->

    <!-- Blog Section - Now Dynamic -->
    <section id="blog" class="py-5 bg-light">
        <div class="container">
            <h2 class="section-title text-center mb-5">Latest From Our Blog</h2>
            <div class="row">
                <!-- First Row - Featured Articles -->
                <?php if (count($featuredPosts) > 0): ?>
                    <div class="col-md-8 mb-4">
                        <div class="blog-card h-100">
                            <div class="blog-img" style="height: 400px;">
                                <img src="<?php echo htmlspecialchars($featuredPosts[0]['image_url']); ?>" alt="<?php echo htmlspecialchars($featuredPosts[0]['title']); ?>">
                            </div>
                            <div class="blog-content">
                                <span class="badge bg-primary mb-2">Featured</span>
                                <p class="text-muted mb-2"><?php echo date('F j, Y', strtotime($featuredPosts[0]['publish_date'])); ?></p>
                                <h3><?php echo htmlspecialchars($featuredPosts[0]['title']); ?></h3>
                                <p><?php echo htmlspecialchars($featuredPosts[0]['excerpt']); ?></p>
                                <a href="article.php?id=<?php echo $featuredPosts[0]['id']; ?>" class="btn btn-primary">Read More</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="row">
                            <?php if (isset($featuredPosts[1])): ?>
                            <div class="col-12 mb-4">
                                <div class="blog-card h-100">
                                    <div class="blog-img">
                                        <img src="<?php echo htmlspecialchars($featuredPosts[1]['image_url']); ?>" alt="<?php echo htmlspecialchars($featuredPosts[1]['title']); ?>">
                                    </div>
                                    <div class="blog-content">
                                        <p class="text-muted mb-2"><?php echo date('F j, Y', strtotime($featuredPosts[1]['publish_date'])); ?></p>
                                        <h4><?php echo htmlspecialchars($featuredPosts[1]['title']); ?></h4>
                                        <p><?php echo htmlspecialchars(substr($featuredPosts[1]['excerpt'], 0, 60)); ?>...</p>
                                        <a href="article.php?id=<?php echo $featuredPosts[1]['id']; ?>" class="btn btn-sm btn-primary">Read More</a>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <?php if (isset($featuredPosts[2])): ?>
                            <div class="col-12">
                                <div class="blog-card h-100">
                                    <div class="blog-img">
                                        <img src="<?php echo htmlspecialchars($featuredPosts[2]['image_url']); ?>" alt="<?php echo htmlspecialchars($featuredPosts[2]['title']); ?>">
                                    </div>
                                    <div class="blog-content">
                                        <p class="text-muted mb-2"><?php echo date('F j, Y', strtotime($featuredPosts[2]['publish_date'])); ?></p>
                                        <h4><?php echo htmlspecialchars($featuredPosts[2]['title']); ?></h4>
                                        <p><?php echo htmlspecialchars(substr($featuredPosts[2]['excerpt'], 0, 60)); ?>...</p>
                                        <a href="article.php?id=<?php echo $featuredPosts[2]['id']; ?>" class="btn btn-sm btn-primary">Read More</a>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Second Row - More Articles -->
                    <div class="row">
                        <?php for ($i = 3; $i < min(count($featuredPosts), 6); $i++): ?>
                        <div class="col-md-4 mb-4">
                            <div class="blog-card h-100">
                                <div class="blog-img">
                                    <img src="<?php echo htmlspecialchars($featuredPosts[$i]['image_url']); ?>" alt="<?php echo htmlspecialchars($featuredPosts[$i]['title']); ?>">
                                </div>
                                <div class="blog-content">
                                    <p class="text-muted mb-2"><?php echo date('F j, Y', strtotime($featuredPosts[$i]['publish_date'])); ?></p>
                                    <h4><?php echo htmlspecialchars($featuredPosts[$i]['title']); ?></h4>
                                    <p><?php echo htmlspecialchars(substr($featuredPosts[$i]['excerpt'], 0, 60)); ?>...</p>
                                    <a href="article.php?id=<?php echo $featuredPosts[$i]['id']; ?>" class="btn btn-sm btn-primary">Read More</a>
                                </div>
                            </div>
                        </div>
                        <?php endfor; ?>
                    </div>
                <?php else: ?>
                    <div class="col-12 text-center">
                        <p>No blog posts available at the moment. Check back soon!</p>
                    </div>
                <?php endif; ?>
                
                <div class="text-center mt-4">
                    <a href="blog.php" class="btn btn-outline-primary">View All Articles</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Keep the rest of the content the same -->
    
    <!-- Fix chat widget to be closed by default -->
    <script>
        // Chat widget functionality
        function toggleChat() {
            const chatContainer = document.getElementById('chatContainer');
            if (chatContainer.style.display === 'block') {
                chatContainer.style.display = 'none';
            } else {
                chatContainer.style.display = 'block';
            }
        }
        
        // Ensure chat is closed by default
        document.addEventListener('DOMContentLoaded', function() {
            const chatContainer = document.getElementById('chatContainer');
            if (chatContainer) {
                chatContainer.style.display = 'none';
            }
        });
        
        // Rest of your existing JavaScript
    </script>
</body>
</html>