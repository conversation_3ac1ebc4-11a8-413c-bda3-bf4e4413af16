<?php
// Database connection
require_once 'includes/db_connect.php';

// Fetch featured blog posts
$sql = "SELECT bp.id, bp.title, bp.slug, bp.excerpt, bp.image_url, bp.publish_date, c.name as category_name
        FROM blog_posts bp
        JOIN categories c ON bp.category_id = c.id
        WHERE bp.status = 'published'
        ORDER BY bp.publish_date DESC
        LIMIT 6";
$result = $conn->query($sql);
$featuredPosts = [];

if ($result->num_rows > 0) {
    while($row = $result->fetch_assoc()) {
        $featuredPosts[] = $row;
    }
}
?>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Lisbon Appliances - Expert refrigerator, washing machine, and cold room repair services in Lisbon and surrounding areas. Fast, reliable service with experienced technicians.">
    <title>Lisbon Appliances - Expert Appliance Repair Services</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/leaflet.css">
    <style>
        :root {
            --primary: #0056b3;
            --secondary: #6c757d;
            --accent: #ffc107;
            --light: #f8f9fa;
            --dark: #343a40;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            color: #333;
            line-height: 1.6;
            overflow-x: hidden;
        }
        
        .navbar {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 15px 0;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.8rem;
            color: var(--primary);
        }
        
        .nav-link {
            font-weight: 500;
            margin: 0 10px;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            color: var(--primary);
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
            padding: 10px 25px;
            font-weight: 600;
            border-radius: 30px;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background-color: #004494;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .hero {
            background: linear-gradient(rgba(0,86,179,0.8), rgba(0,86,179,0.9)), url('https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80') center/cover no-repeat;
            height: 85vh;
            display: flex;
            align-items: center;
            color: white;
            position: relative;
        }
        
        .hero-content {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        
        .hero h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 20px;
        }
        
        .hero p {
            font-size: 1.2rem;
            margin-bottom: 30px;
        }
        
        .service-card {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.05);
            margin-bottom: 30px;
            transition: all 0.3s ease;
            height: 100%;
        }
        
        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        }
        
        .service-card i {
            font-size: 3rem;
            color: var(--primary);
            margin-bottom: 20px;
        }
        
        .service-card h3 {
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .section-title {
            position: relative;
            margin-bottom: 50px;
            font-weight: 700;
            display: inline-block;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 50px;
            height: 3px;
            background-color: var(--primary);
        }
        
        .about-img {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .testimonial-card {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.05);
            margin: 15px;
        }
        
        .testimonial-card img {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            object-fit: cover;
            margin-bottom: 15px;
        }
        
        .testimonial-card .stars {
            color: var(--accent);
            margin-bottom: 15px;
        }
        
        .blog-card {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0,0,0,0.05);
            margin-bottom: 30px;
            transition: all 0.3s ease;
        }
        
        .blog-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        }
        
        .blog-img {
            height: 200px;
            overflow: hidden;
        }
        
        .blog-img img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }
        
        .blog-card:hover .blog-img img {
            transform: scale(1.1);
        }
        
        .blog-content {
            padding: 20px;
        }
        
        .blog-content h4 {
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .faq-item {
            margin-bottom: 20px;
        }
        
        .faq-question {
            font-weight: 600;
            cursor: pointer;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .faq-answer {
            padding: 15px;
            display: none;
        }
        
        #map {
            height: 400px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0,0,0,0.05);
        }
        
        .contact-info i {
            color: var(--primary);
            font-size: 1.5rem;
            margin-right: 10px;
        }
        
        footer {
            background-color: var(--dark);
            color: white;
            padding: 50px 0 20px;
        }
        
        .footer-title {
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--light);
        }
        
        .footer-links a {
            color: #adb5bd;
            text-decoration: none;
            display: block;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        
        .footer-links a:hover {
            color: white;
            transform: translateX(5px);
        }
        
        .social-links a {
            display: inline-block;
            width: 40px;
            height: 40px;
            background-color: rgba(255,255,255,0.1);
            border-radius: 50%;
            text-align: center;
            line-height: 40px;
            color: white;
            margin-right: 10px;
            transition: all 0.3s ease;
        }
        
        .social-links a:hover {
            background-color: var(--primary);
            transform: translateY(-3px);
        }
        
        .copyright {
            padding-top: 20px;
            border-top: 1px solid rgba(255,255,255,0.1);
            margin-top: 30px;
            text-align: center;
            color: #adb5bd;
        }
        
        .scroll-top {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: var(--primary);
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            z-index: 99;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
        
        .scroll-top.active {
            opacity: 1;
            visibility: visible;
        }
        
        /* Chat widget */
        .chat-widget {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .chat-button {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: var(--primary);
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .chat-button i {
            font-size: 24px;
        }
        
        .chat-container {
            position: absolute;
            bottom: 70px;
            right: 0;
            width: 350px;
            height: 450px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 30px rgba(0,0,0,0.1);
            overflow: hidden;
            display: none;
            display: flex;
            flex-direction: column;
        }
        
        .chat-header {
            background-color: var(--primary);
            color: white;
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .chat-body {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
            max-height: 320px;
        }
        
        .chat-message {
            margin-bottom: 15px;
            display: flex;
        }
        
        .chat-message.incoming {
            justify-content: flex-start;
        }
        
        .chat-message.outgoing {
            justify-content: flex-end;
        }
        
        .chat-bubble {
            max-width: 70%;
            padding: 10px 15px;
            border-radius: 20px;
        }
        
        .chat-message.incoming .chat-bubble {
            background-color: #f0f0f0;
        }
        
        .chat-message.outgoing .chat-bubble {
            background-color: var(--primary);
            color: white;
        }
        
        .chat-input {
            display: flex;
            padding: 15px;
            border-top: 1px solid #f0f0f0;
            background-color: white;
            border-radius: 0 0 10px 10px;
            flex-shrink: 0;
        }
        
        .chat-input input {
            flex-grow: 1;
            border: 1px solid #e0e0e0;
            outline: none;
            padding: 12px 15px;
            border-radius: 25px;
            background-color: #f8f9fa;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .chat-input input:focus {
            border-color: var(--primary);
            background-color: white;
            box-shadow: 0 0 0 2px rgba(0, 86, 179, 0.1);
        }
        
        .chat-input button {
            background-color: var(--primary);
            color: white;
            border: none;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            margin-left: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            flex-shrink: 0;
        }

        .chat-input button:hover {
            background-color: #004494;
            transform: scale(1.05);
        }

        /* Chat option buttons */
        .chat-bubble .btn {
            font-size: 0.8rem;
            padding: 5px 10px;
            border-radius: 15px;
            transition: all 0.3s ease;
        }

        .chat-bubble .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }

            .hero {
                height: 75vh;
            }

            .chat-container {
                width: 300px;
                height: 400px;
                bottom: 80px;
                right: 10px;
            }

            .chat-body {
                max-height: 280px;
            }

            .fixed-action-btn {
                bottom: 80px;
                right: 10px;
            }
        }
        
        .service-section {
            background-color: var(--light);
            padding: 80px 0;
        }

        .service-card-advanced {
            position: relative;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .service-card-advanced:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .service-card-advanced .service-image {
            height: 250px;
            overflow: hidden;
            position: relative;
        }

        .service-card-advanced .service-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.4s ease;
        }

        .service-card-advanced:hover .service-image img {
            transform: scale(1.1);
        }

        .service-card-advanced .service-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(to bottom, transparent, rgba(0,0,0,0.7));
            overflow: hidden;
            width: 100%;
            height: 0;
            transition: 0.5s ease;
        }

        .service-card-advanced:hover .service-overlay {
            height: 100%;
        }

        .service-card-advanced .service-content {
            padding: 20px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }

        .service-card-advanced .service-title {
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 15px;
            font-size: 1.4rem;
        }

        .service-card-advanced .service-description {
            flex-grow: 1;
            color: #6c757d;
            margin-bottom: 15px;
        }

        .service-card-advanced .service-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: auto;
        }

        .service-card-advanced .service-icon {
            font-size: 2.5rem;
            color: var(--primary);
            opacity: 0.7;
        }

        .service-card-advanced .service-link {
            color: var(--primary);
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .service-card-advanced .service-link:hover {
            color: #004494;
        }

        /* Contact Section Styles */
        .contact-info {
            transition: all 0.3s ease;
        }

        .contact-info:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
        }

        .contact-info a {
            transition: color 0.3s ease;
        }

        .contact-info a:hover {
            color: var(--primary) !important;
        }

        /* Fixed Action Buttons */
        .fixed-action-btn {
            position: fixed;
            bottom: 100px;
            right: 20px;
            z-index: 1000;
        }

        .fixed-action-btn .btn {
            margin-bottom: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
        }

        .fixed-action-btn .btn:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
        }

        /* WhatsApp Button Styling */
        .btn-whatsapp {
            background-color: #25D366;
            border-color: #25D366;
            color: white;
        }

        .btn-whatsapp:hover {
            background-color: #128C7E;
            border-color: #128C7E;
            color: white;
        }

        /* Contact Section Background */
        #contact {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        /* Enhanced Map Styling */
        #map {
            border: 3px solid var(--primary);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="#">Lisbon Appliances</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#home">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">About Us</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#services">Services</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#testimonials">Testimonials</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="blog.php">Blog</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#faq">FAQ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">Contact</a>
                    </li>
                </ul>
                <a href="#appointment" class="btn btn-primary ms-3">Schedule Repair</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero" style="background: linear-gradient(rgba(0,86,179,0.8), rgba(0,86,179,0.9)), url('FridgeRep.jpg') center/cover no-repeat; height: 85vh;">
        <div class="container">
            <div class="hero-content">
                <h1>Expert Appliance Repair in Lisbon</h1>
                <p>Specializing in refrigerator, washing machine, and cold room repair services with fast response times and transparent pricing.</p>
                <div class="d-flex justify-content-center">
                    <a href="#appointment" class="btn btn-primary me-3">Schedule Your Repair</a>
                    <a href="#services" class="btn btn-outline-light">Our Services</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="service-section">
        <div class="container">
            <h2 class="section-title text-center mb-5">Our Expert Services</h2>
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="service-card-advanced">
                        <div class="service-image" style="display: flex;">
                            <img src="refrigerator-repairman.png" alt="Refrigerator Repairman" style="width: 50%; height: auto; object-fit: cover;">
                            <img src="Fridge_B.jpg" alt="Refrigerator Repair" style="width: 50%; height: auto; object-fit: cover;">
                            <div class="service-overlay"></div>
                        </div>
                        <div class="service-content">
                            <h3 class="service-title">Refrigerator Repair</h3>
                            <p class="service-description">Expert solutions for cooling issues, strange noises, water leaks, and comprehensive refrigerator diagnostics.</p>
                            <div class="service-details">
                                <i class="fas fa-temperature-low service-icon"></i>
                                <a href="#appointment" class="service-link">Book Service <i class="fas fa-arrow-right ms-2"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="service-card-advanced">
                        <div class="service-image">
                            <img src="https://images.unsplash.com/photo-1610557892470-55d9e80c0bce" alt="Washing Machine Repair">
                            <div class="service-overlay"></div>
                        </div>
                        <div class="service-content">
                            <h3 class="service-title">Washing Machine Repair</h3>
                            <p class="service-description">Addressing drainage problems, spin cycle issues, leaks, and electronic control system malfunctions.</p>
                            <div class="service-details">
                                <i class="fas fa-tint service-icon"></i>
                                <a href="#appointment" class="service-link">Book Service <i class="fas fa-arrow-right ms-2"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="service-card-advanced">
                        <div class="service-image">
                            <img src="coldroom.jpg" alt="Cold Room Repair">
                            <div class="service-overlay"></div>
                        </div>
                        <div class="service-content">
                            <h3 class="service-title">Cold Room Repair</h3>
                            <p class="service-description">Specialized commercial repair for temperature control systems, compressor issues, and preventative maintenance.</p>
                            <div class="service-details">
                                <i class="fas fa-snowflake service-icon"></i>
                                <a href="#appointment" class="service-link">Book Service <i class="fas fa-arrow-right ms-2"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-5 bg-light">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6 mb-4 mb-lg-0">
                    <img src="LSWorker_BackwithLogo (2).jpg" alt="Lisbon Appliances Team" class="img-fluid about-img">
                </div>
                <div class="col-lg-6">
                    <h2 class="section-title">About Lisbon Appliances</h2>
                    <p>Founded in 2010, Lisbon Appliances has been the trusted partner for appliance repairs across the Lisbon metropolitan area for over a decade. What started as a small family business has grown into a team of certified technicians dedicated to providing top-quality repair services.</p>
                    <p>Our mission is simple: to provide fast, reliable, and affordable appliance repair services that exceed your expectations. We understand the inconvenience of malfunctioning appliances and work diligently to restore them to optimal condition as quickly as possible.</p>
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <i class="fas fa-check-circle text-primary me-2"></i>
                                <p class="mb-0">Certified Technicians</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <i class="fas fa-check-circle text-primary me-2"></i>
                                <p class="mb-0">Warranty on Repairs</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <i class="fas fa-check-circle text-primary me-2"></i>
                                <p class="mb-0">Genuine Replacement Parts</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <i class="fas fa-check-circle text-primary me-2"></i>
                                <p class="mb-0">Competitive Pricing</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Appointment Section -->
    <section id="appointment" class="py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-lg-6 mx-auto">
                    <h2 class="section-title text-center mb-5">Schedule Your Repair</h2>
                    <form action="https://formsubmit.co/<EMAIL>" method="POST" class="p-4 bg-white rounded shadow-sm">
                        <input type="hidden" name="_next" value="https://metallinengineering.co.za/contact.html?success=true">
                        <input type="hidden" name="_captcha" value="false">
                        <div class="mb-3">
                            <label for="name" class="form-label">Full Name</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        <div class="mb-3">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="phone" name="phone" required>
                        </div>
                        <div class="mb-3">
                            <label for="service" class="form-label">Service Needed</label>
                            <select class="form-select" id="service" name="service" required>
                                <option value="" selected disabled>Choose a service...</option>
                                <option value="refrigerator">Refrigerator Repair</option>
                                <option value="washing-machine">Washing Machine Repair</option>
                                <option value="cold-room">Cold Room Repair</option>
                                <option value="other">Other (specify in details)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="appliance-brand" class="form-label">Appliance Brand & Model (if known)</label>
                            <input type="text" class="form-control" id="appliance-brand" name="appliance_brand">
                        </div>
                        <div class="mb-3">
                            <label for="date" class="form-label">Preferred Date</label>
                            <input type="date" class="form-control" id="date" name="date" required>
                        </div>
                        <div class="mb-3">
                            <label for="time" class="form-label">Preferred Time</label>
                            <select class="form-select" id="time" name="time" required>
                                <option value="" selected disabled>Choose a time slot...</option>
                                <option value="morning">Morning (9am - 12pm)</option>
                                <option value="afternoon">Afternoon (12pm - 3pm)</option>
                                <option value="evening">Evening (3pm - 6pm)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="issue" class="form-label">Describe the Issue</label>
                            <textarea class="form-control" id="issue" name="issue" rows="3" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control" id="address" name="address" rows="2" required></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">Schedule Repair</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Blog Section -->
    <section id="blog" class="py-5 bg-light">
        <div class="container">
            <h2 class="section-title text-center mb-5">Latest From Our Blog</h2>
            <div class="row">
                <!-- First Row - Featured Articles -->
                <?php if (count($featuredPosts) > 0): ?>
                    <div class="col-md-8 mb-4">
                        <div class="blog-card h-100">
                            <div class="blog-img" style="height: 400px;">
                                <img src="<?php echo htmlspecialchars($featuredPosts[0]['image_url']); ?>" alt="<?php echo htmlspecialchars($featuredPosts[0]['title']); ?>">
                            </div>
                            <div class="blog-content">
                                <span class="badge bg-primary mb-2">Featured</span>
                                <p class="text-muted mb-2"><?php echo date('F j, Y', strtotime($featuredPosts[0]['publish_date'])); ?></p>
                                <h3><?php echo htmlspecialchars($featuredPosts[0]['title']); ?></h3>
                                <p><?php echo htmlspecialchars($featuredPosts[0]['excerpt']); ?></p>
                                <a href="article.php?slug=<?php echo urlencode($featuredPosts[0]['slug']); ?>" class="btn btn-primary">Read More</a>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="row">
                            <?php if (isset($featuredPosts[1])): ?>
                            <div class="col-12 mb-4">
                                <div class="blog-card h-100">
                                    <div class="blog-img">
                                        <img src="<?php echo htmlspecialchars($featuredPosts[1]['image_url']); ?>" alt="<?php echo htmlspecialchars($featuredPosts[1]['title']); ?>">
                                    </div>
                                    <div class="blog-content">
                                        <p class="text-muted mb-2"><?php echo date('F j, Y', strtotime($featuredPosts[1]['publish_date'])); ?></p>
                                        <h4><?php echo htmlspecialchars($featuredPosts[1]['title']); ?></h4>
                                        <p><?php echo htmlspecialchars(substr($featuredPosts[1]['excerpt'], 0, 60)); ?>...</p>
                                        <a href="article.php?slug=<?php echo urlencode($featuredPosts[1]['slug']); ?>" class="btn btn-sm btn-primary">Read More</a>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>

                            <?php if (isset($featuredPosts[2])): ?>
                            <div class="col-12">
                                <div class="blog-card h-100">
                                    <div class="blog-img">
                                        <img src="<?php echo htmlspecialchars($featuredPosts[2]['image_url']); ?>" alt="<?php echo htmlspecialchars($featuredPosts[2]['title']); ?>">
                                    </div>
                                    <div class="blog-content">
                                        <p class="text-muted mb-2"><?php echo date('F j, Y', strtotime($featuredPosts[2]['publish_date'])); ?></p>
                                        <h4><?php echo htmlspecialchars($featuredPosts[2]['title']); ?></h4>
                                        <p><?php echo htmlspecialchars(substr($featuredPosts[2]['excerpt'], 0, 60)); ?>...</p>
                                        <a href="article.php?slug=<?php echo urlencode($featuredPosts[2]['slug']); ?>" class="btn btn-sm btn-primary">Read More</a>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Second Row - More Articles -->
                    <?php for ($i = 3; $i < min(6, count($featuredPosts)); $i++): ?>
                        <div class="col-md-4 mb-4">
                            <div class="blog-card h-100">
                                <div class="blog-img">
                                    <img src="<?php echo htmlspecialchars($featuredPosts[$i]['image_url']); ?>" alt="<?php echo htmlspecialchars($featuredPosts[$i]['title']); ?>">
                                </div>
                                <div class="blog-content">
                                    <p class="text-muted mb-2"><?php echo date('F j, Y', strtotime($featuredPosts[$i]['publish_date'])); ?></p>
                                    <h4><?php echo htmlspecialchars($featuredPosts[$i]['title']); ?></h4>
                                    <p><?php echo htmlspecialchars(substr($featuredPosts[$i]['excerpt'], 0, 60)); ?>...</p>
                                    <a href="article.php?slug=<?php echo urlencode($featuredPosts[$i]['slug']); ?>" class="btn btn-sm btn-primary">Read More</a>
                                </div>
                            </div>
                        </div>
                    <?php endfor; ?>
                <?php else: ?>
                    <div class="col-12 text-center">
                        <p>No blog posts available yet. <a href="blog.php">Visit our blog</a> to see all articles.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section id="testimonials" class="py-5">
        <div class="container">
            <h2 class="section-title text-center mb-5">What Our Customers Say</h2>
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="testimonial-card text-center">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face" alt="Customer 1">
                        <div class="stars mb-3">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <p>"Excellent service! My refrigerator was fixed within hours and the technician was very professional. Highly recommend Lisbon Appliances."</p>
                        <h5>John Smith</h5>
                        <small class="text-muted">Kempton Park</small>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="testimonial-card text-center">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face" alt="Customer 2">
                        <div class="stars mb-3">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <p>"Fast response time and fair pricing. They diagnosed and fixed my washing machine problem quickly. Great customer service!"</p>
                        <h5>Sarah Johnson</h5>
                        <small class="text-muted">Norkem Park</small>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="testimonial-card text-center">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face" alt="Customer 3">
                        <div class="stars mb-3">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <p>"Professional technicians who know their work. Fixed our commercial cold room efficiently. Will definitely use their services again."</p>
                        <h5>Mike Davis</h5>
                        <small class="text-muted">Lisbon</small>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section id="faq" class="py-5 bg-light">
        <div class="container">
            <h2 class="section-title text-center mb-5">Frequently Asked Questions</h2>
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="faq-item">
                        <div class="faq-question" onclick="toggleFaq(this)">
                            <span>How quickly can you respond to repair requests?</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>We typically respond to repair requests within 24 hours and can often schedule same-day or next-day appointments depending on availability and urgency.</p>
                        </div>
                    </div>
                    <div class="faq-item">
                        <div class="faq-question" onclick="toggleFaq(this)">
                            <span>Do you provide warranties on your repairs?</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>Yes, we provide a 90-day warranty on all repairs and a 1-year warranty on replacement parts. This ensures your peace of mind and our commitment to quality service.</p>
                        </div>
                    </div>
                    <div class="faq-item">
                        <div class="faq-question" onclick="toggleFaq(this)">
                            <span>What brands of appliances do you service?</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>We service all major appliance brands including Samsung, LG, Whirlpool, Bosch, Defy, and many others. Our technicians are trained to work on both domestic and commercial appliances.</p>
                        </div>
                    </div>
                    <div class="faq-item">
                        <div class="faq-question" onclick="toggleFaq(this)">
                            <span>Do you charge for diagnostics?</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>We charge a small diagnostic fee which is waived if you proceed with the repair. This ensures we can provide thorough diagnostics while keeping costs fair for all customers.</p>
                        </div>
                    </div>
                    <div class="faq-item">
                        <div class="faq-question" onclick="toggleFaq(this)">
                            <span>What payment methods do you accept?</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>We accept cash, bank transfers, and all major credit cards. Payment is due upon completion of the repair service.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-5">
        <div class="container">
            <h2 class="section-title text-center mb-5">Get In Touch</h2>
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="contact-info bg-white p-4 rounded shadow-sm h-100">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="fas fa-phone-alt text-primary me-3" style="font-size: 2rem;"></i>
                                    <div>
                                        <h5 class="mb-1">Phone Number</h5>
                                        <p class="mb-0">
                                            <a href="tel:0691437683" class="text-decoration-none text-dark">************</a>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div class="contact-info bg-white p-4 rounded shadow-sm h-100">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="fab fa-whatsapp text-success me-3" style="font-size: 2rem;"></i>
                                    <div>
                                        <h5 class="mb-1">WhatsApp</h5>
                                        <p class="mb-0">
                                            <a href="https://wa.me/27691437683" class="text-decoration-none text-dark" target="_blank">************</a>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div class="contact-info bg-white p-4 rounded shadow-sm h-100">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="fas fa-envelope text-primary me-3" style="font-size: 2rem;"></i>
                                    <div>
                                        <h5 class="mb-1">Email</h5>
                                        <p class="mb-0">
                                            <a href="mailto:<EMAIL>" class="text-decoration-none text-dark"><EMAIL></a>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div class="contact-info bg-white p-4 rounded shadow-sm h-100">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="fas fa-map-marker-alt text-primary me-3" style="font-size: 2rem;"></i>
                                    <div>
                                        <h5 class="mb-1">Address</h5>
                                        <p class="mb-0">44 Tortelduif Drive<br>Norkem Park<br>Kempton Park, 1618</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Action Buttons -->
                    <div class="text-center mt-4">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <a href="#appointment" class="btn btn-primary btn-lg w-100">
                                    <i class="fas fa-calendar-alt me-2"></i>Schedule a Repair
                                </a>
                            </div>
                            <div class="col-md-6 mb-3">
                                <a href="https://wa.me/27691437683?text=Hi%20Lisbon%20Appliances,%20I%20need%20help%20with%20my%20appliance"
                                   class="btn btn-success btn-lg w-100" target="_blank">
                                    <i class="fab fa-whatsapp me-2"></i>WhatsApp Us
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Map Section -->
            <div class="row mt-5">
                <div class="col-12">
                    <div id="map"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h4 class="footer-title">Lisbon Appliances</h4>
                    <p>Your trusted partner for professional appliance repair services in Lisbon and surrounding areas. Fast, reliable, and affordable solutions for all your appliance needs.</p>
                    <div class="social-links">
                        <a href="#" title="Facebook"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" title="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" title="Instagram"><i class="fab fa-instagram"></i></a>
                        <a href="https://wa.me/27691437683" title="WhatsApp" target="_blank"><i class="fab fa-whatsapp"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5 class="footer-title">Services</h5>
                    <div class="footer-links">
                        <a href="#services">Refrigerator Repair</a>
                        <a href="#services">Washing Machine Repair</a>
                        <a href="#services">Cold Room Repair</a>
                        <a href="#appointment">Emergency Repairs</a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5 class="footer-title">Quick Links</h5>
                    <div class="footer-links">
                        <a href="#about">About Us</a>
                        <a href="blog.php">Blog</a>
                        <a href="#faq">FAQ</a>
                        <a href="#contact">Contact</a>
                    </div>
                </div>
                <div class="col-lg-4 mb-4">
                    <h5 class="footer-title">Contact Info</h5>
                    <div class="contact-info">
                        <p><i class="fas fa-phone"></i> ************</p>
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-map-marker-alt"></i> 44 Tortelduif Drive, Norkem Park, Kempton Park, 1618</p>
                    </div>
                </div>
            </div>
            <div class="copyright">
                <p>&copy; 2023 Lisbon Appliances. All rights reserved. | Professional appliance repair services in Lisbon and surrounding areas.</p>
            </div>
        </div>
    </footer>

    <!-- Scroll to Top Button -->
    <div class="scroll-top" onclick="scrollToTop()">
        <i class="fas fa-chevron-up"></i>
    </div>

    <!-- Chat Widget -->
    <div class="chat-widget">
        <div class="chat-button" onclick="toggleChat()">
            <i class="fas fa-comments"></i>
        </div>
        <div class="chat-container" id="chatContainer">
            <div class="chat-header">
                <span>Chat with us</span>
                <i class="fas fa-times" onclick="toggleChat()"></i>
            </div>
            <div class="chat-body" id="chatBody">
                <div class="chat-message incoming">
                    <div class="chat-bubble">
                        👋 Hello! Welcome to Lisbon Appliances! How can we help you today?
                    </div>
                </div>
                <div class="chat-message incoming">
                    <div class="chat-bubble">
                        Please select one of the options below:
                        <div class="mt-2">
                            <button class="btn btn-sm btn-primary me-2 mb-1" onclick="requestContactDetails()">📞 Get Contact Details</button>
                            <button class="btn btn-sm btn-success mb-1" onclick="scheduleAppointment()">📅 Schedule Repair</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="chat-input">
                <input type="text" placeholder="Type your message..." id="chatInput" onkeypress="handleChatKeyPress(event)">
                <button onclick="sendMessage()"><i class="fas fa-paper-plane"></i></button>
            </div>
        </div>
    </div>

    <!-- Fixed Action Buttons -->
    <div class="fixed-action-btn">
        <div>
            <a href="#appointment" class="btn btn-primary rounded-circle" style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;" title="Schedule Repair">
                <i class="fas fa-calendar-alt"></i>
            </a>
        </div>
        <div>
            <a href="#contact" class="btn btn-info rounded-circle" style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;" title="Contact Us">
                <i class="fas fa-phone"></i>
            </a>
        </div>
        <div>
            <a href="https://wa.me/27691437683" class="btn btn-whatsapp rounded-circle" style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;" title="WhatsApp Us" target="_blank">
                <i class="fab fa-whatsapp"></i>
            </a>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Leaflet JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/leaflet.js"></script>

    <script>
        // Initialize map
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Leaflet map for Kempton Park area
            var map = L.map('map').setView([-26.1186, 28.2294], 13);

            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);

            // Add marker for business location
            L.marker([-26.1186, 28.2294]).addTo(map)
                .bindPopup('<b>Lisbon Appliances</b><br>44 Tortelduif Drive<br>Norkem Park, Kempton Park')
                .openPopup();
        });

        // FAQ Toggle Function
        function toggleFaq(element) {
            const answer = element.nextElementSibling;
            const icon = element.querySelector('i');

            if (answer.style.display === 'block') {
                answer.style.display = 'none';
                icon.classList.remove('fa-chevron-up');
                icon.classList.add('fa-chevron-down');
            } else {
                answer.style.display = 'block';
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-up');
            }
        }

        // Scroll to top function
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // Show/hide scroll to top button
        window.addEventListener('scroll', function() {
            const scrollTop = document.querySelector('.scroll-top');
            if (window.pageYOffset > 300) {
                scrollTop.classList.add('active');
            } else {
                scrollTop.classList.remove('active');
            }
        });

        // Chat widget functionality
        function toggleChat() {
            const chatContainer = document.getElementById('chatContainer');
            if (chatContainer.style.display === 'block') {
                chatContainer.style.display = 'none';
            } else {
                chatContainer.style.display = 'block';
            }
        }

        function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();

            if (message) {
                const chatBody = document.getElementById('chatBody');

                // Add user message
                const userMessage = document.createElement('div');
                userMessage.className = 'chat-message outgoing';
                userMessage.innerHTML = `<div class="chat-bubble">${message}</div>`;
                chatBody.appendChild(userMessage);

                // Clear input
                input.value = '';

                // Auto-response
                setTimeout(() => {
                    const botMessage = document.createElement('div');
                    botMessage.className = 'chat-message incoming';
                    botMessage.innerHTML = `<div class="chat-bubble">Thank you for your message! For immediate assistance, please call us at <a href="tel:0691437683">************</a> or <a href="https://wa.me/27691437683" target="_blank">WhatsApp us</a>. We'll get back to you as soon as possible.</div>`;
                    chatBody.appendChild(botMessage);
                    chatBody.scrollTop = chatBody.scrollHeight;
                }, 1000);

                chatBody.scrollTop = chatBody.scrollHeight;
            }
        }

        function handleChatKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // Chatbot option functions
        function requestContactDetails() {
            const chatBody = document.getElementById('chatBody');

            // Add user selection message
            const userMessage = document.createElement('div');
            userMessage.className = 'chat-message outgoing';
            userMessage.innerHTML = `<div class="chat-bubble">📞 Get Contact Details</div>`;
            chatBody.appendChild(userMessage);

            // Bot response with contact details
            setTimeout(() => {
                const botMessage = document.createElement('div');
                botMessage.className = 'chat-message incoming';
                botMessage.innerHTML = `
                    <div class="chat-bubble">
                        Here are our contact details:<br><br>
                        📞 <strong>Phone:</strong> <a href="tel:0691437683" style="color: #0056b3;">************</a><br>
                        💬 <strong>WhatsApp:</strong> <a href="https://wa.me/27691437683" target="_blank" style="color: #25D366;">************</a><br>
                        ✉️ <strong>Email:</strong> <a href="mailto:<EMAIL>" style="color: #0056b3;"><EMAIL></a><br>
                        📍 <strong>Address:</strong> 44 Tortelduif Drive, Norkem Park, Kempton Park, 1618<br><br>
                        Feel free to contact us anytime! 😊
                    </div>
                `;
                chatBody.appendChild(botMessage);
                chatBody.scrollTop = chatBody.scrollHeight;
            }, 1000);

            chatBody.scrollTop = chatBody.scrollHeight;
        }

        function scheduleAppointment() {
            const chatBody = document.getElementById('chatBody');

            // Add user selection message
            const userMessage = document.createElement('div');
            userMessage.className = 'chat-message outgoing';
            userMessage.innerHTML = `<div class="chat-bubble">📅 Schedule Repair</div>`;
            chatBody.appendChild(userMessage);

            // Bot response with appointment link
            setTimeout(() => {
                const botMessage = document.createElement('div');
                botMessage.className = 'chat-message incoming';
                botMessage.innerHTML = `
                    <div class="chat-bubble">
                        Great! I'll help you schedule a repair appointment. 📅<br><br>
                        Please click the link below to fill out our appointment form:<br><br>
                        <a href="#appointment" onclick="toggleChat(); document.getElementById('appointment').scrollIntoView({behavior: 'smooth'});" style="background-color: #0056b3; color: white; padding: 10px 15px; border-radius: 5px; text-decoration: none; display: inline-block;">
                            📝 Schedule Your Repair
                        </a><br><br>
                        Or you can call us directly at <a href="tel:0691437683" style="color: #0056b3;">************</a> for immediate assistance! 📞
                    </div>
                `;
                chatBody.appendChild(botMessage);
                chatBody.scrollTop = chatBody.scrollHeight;
            }, 1000);

            chatBody.scrollTop = chatBody.scrollHeight;
        }

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Form validation and enhancement
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    // Add loading state to submit button
                    const submitBtn = form.querySelector('button[type="submit"]');
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Scheduling...';
                    submitBtn.disabled = true;
                });
            }
        });

        // Add animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe elements for animation
        document.addEventListener('DOMContentLoaded', function() {
            const animateElements = document.querySelectorAll('.service-card-advanced, .testimonial-card, .blog-card, .contact-info');
            animateElements.forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(30px)';
                el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(el);
            });
        });
    </script>
</body>
</html>
