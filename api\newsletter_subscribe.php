<?php
require_once '../includes/db_connect.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Get input data
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    // Try form data
    $email = $_POST['email'] ?? '';
    $name = $_POST['name'] ?? '';
} else {
    $email = $input['email'] ?? '';
    $name = $input['name'] ?? '';
}

// Validate email
if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
    http_response_code(400);
    echo json_encode(['error' => 'Valid email address is required']);
    exit;
}

// Sanitize inputs
$email = $conn->real_escape_string(strtolower(trim($email)));
$name = $conn->real_escape_string(trim($name));
$ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
$user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

// Check if email already exists
$check_sql = "SELECT id, status FROM newsletter_subscribers WHERE email = '$email'";
$check_result = $conn->query($check_sql);

if ($check_result->num_rows > 0) {
    $existing = $check_result->fetch_assoc();
    
    if ($existing['status'] == 'active') {
        echo json_encode(['message' => 'You are already subscribed to our newsletter!']);
        exit;
    } else {
        // Reactivate subscription
        $update_sql = "UPDATE newsletter_subscribers SET 
                       status = 'active', 
                       name = '$name',
                       subscribed_at = NOW(),
                       unsubscribed_at = NULL,
                       ip_address = '$ip_address',
                       user_agent = '$user_agent'
                       WHERE email = '$email'";
        
        if ($conn->query($update_sql)) {
            echo json_encode(['message' => 'Welcome back! Your subscription has been reactivated.']);
        } else {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to reactivate subscription']);
        }
        exit;
    }
}

// Insert new subscription
$insert_sql = "INSERT INTO newsletter_subscribers (email, name, ip_address, user_agent) 
               VALUES ('$email', '$name', '$ip_address', '$user_agent')";

if ($conn->query($insert_sql)) {
    // Send welcome email (optional - you can implement this later)
    // sendWelcomeEmail($email, $name);
    
    echo json_encode(['message' => 'Thank you for subscribing! You will receive our latest appliance tips and maintenance guides.']);
} else {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to subscribe. Please try again.']);
}

$conn->close();

// Function to send welcome email (implement with your preferred email service)
function sendWelcomeEmail($email, $name) {
    // This is a placeholder - implement with your email service
    // You could use PHPMailer, SendGrid, Mailgun, etc.
    
    $subject = "Welcome to Lisbon Appliances Newsletter!";
    $message = "
    <html>
    <head>
        <title>Welcome to Lisbon Appliances Newsletter</title>
    </head>
    <body>
        <h2>Welcome" . ($name ? ", " . htmlspecialchars($name) : "") . "!</h2>
        <p>Thank you for subscribing to the Lisbon Appliances newsletter.</p>
        <p>You'll receive:</p>
        <ul>
            <li>Expert appliance repair tips</li>
            <li>Maintenance guides</li>
            <li>Energy-saving advice</li>
            <li>Special offers and updates</li>
        </ul>
        <p>Best regards,<br>The Lisbon Appliances Team</p>
        <hr>
        <small>
            If you didn't subscribe to this newsletter, please ignore this email.
            <br>
            Lisbon Appliances | 44 Tortelduif Drive, Norkem Park, Kempton Park
        </small>
    </body>
    </html>
    ";
    
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= "From: Lisbon Appliances <<EMAIL>>" . "\r\n";
    
    // Uncomment to actually send emails
    // mail($email, $subject, $message, $headers);
}
?>
