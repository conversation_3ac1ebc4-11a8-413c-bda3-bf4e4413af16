<?php
session_start();
require_once '../includes/db_connect.php';
require_once 'includes/auth_check.php';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Content Assistant - Lisbon Appliances Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/admin.css">
    <style>
        .chat-container {
            height: 600px;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            display: flex;
            flex-direction: column;
        }
        .chat-header {
            background: linear-gradient(135deg, #0056b3, #004494);
            color: white;
            padding: 1rem;
            border-radius: 10px 10px 0 0;
        }
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            background: #f8f9fa;
        }
        .message {
            margin-bottom: 1rem;
            display: flex;
            align-items: flex-start;
        }
        .message.user {
            justify-content: flex-end;
        }
        .message-bubble {
            max-width: 70%;
            padding: 0.75rem 1rem;
            border-radius: 18px;
            word-wrap: break-word;
        }
        .message.user .message-bubble {
            background: #0056b3;
            color: white;
            border-bottom-right-radius: 5px;
        }
        .message.ai .message-bubble {
            background: white;
            border: 1px solid #dee2e6;
            border-bottom-left-radius: 5px;
        }
        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 0.5rem;
            font-size: 0.8rem;
        }
        .message.user .message-avatar {
            background: #0056b3;
            color: white;
            order: 2;
        }
        .message.ai .message-avatar {
            background: #28a745;
            color: white;
        }
        .chat-input {
            padding: 1rem;
            border-top: 1px solid #dee2e6;
            background: white;
            border-radius: 0 0 10px 10px;
        }
        .typing-indicator {
            display: none;
            padding: 0.5rem 1rem;
            color: #6c757d;
            font-style: italic;
        }
        .content-type-selector {
            margin-bottom: 1rem;
        }
        .generated-content {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }
        .copy-btn {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
        }
        .quick-prompts {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        .quick-prompt {
            background: #e9ecef;
            border: none;
            padding: 0.25rem 0.5rem;
            border-radius: 15px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.2s;
        }
        .quick-prompt:hover {
            background: #0056b3;
            color: white;
        }
    </style>
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="fas fa-robot me-2"></i>AI Content Assistant</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button class="btn btn-sm btn-outline-secondary" onclick="clearChat()">
                            <i class="fas fa-trash me-1"></i>Clear Chat
                        </button>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-8">
                        <div class="chat-container">
                            <div class="chat-header">
                                <h5 class="mb-0"><i class="fas fa-magic me-2"></i>AI Content Generator</h5>
                                <small>Powered by Google Gemini</small>
                            </div>
                            
                            <div class="chat-messages" id="chatMessages">
                                <div class="message ai">
                                    <div class="message-avatar">
                                        <i class="fas fa-robot"></i>
                                    </div>
                                    <div class="message-bubble">
                                        <strong>Welcome to the AI Content Assistant!</strong><br>
                                        I can help you generate blog posts, titles, excerpts, and more for Lisbon Appliances. 
                                        Select a content type and describe what you'd like me to create.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="typing-indicator" id="typingIndicator">
                                <i class="fas fa-circle-notch fa-spin me-2"></i>AI is thinking...
                            </div>
                            
                            <div class="chat-input">
                                <div class="content-type-selector">
                                    <label class="form-label">Content Type:</label>
                                    <select class="form-select form-select-sm" id="contentType">
                                        <option value="content">Full Blog Post</option>
                                        <option value="title">Blog Title</option>
                                        <option value="excerpt">Blog Excerpt</option>
                                    </select>
                                </div>
                                
                                <div class="quick-prompts" id="quickPrompts">
                                    <button class="quick-prompt" onclick="useQuickPrompt('Common refrigerator problems and solutions')">Refrigerator Issues</button>
                                    <button class="quick-prompt" onclick="useQuickPrompt('Washing machine maintenance tips')">Washing Machine Tips</button>
                                    <button class="quick-prompt" onclick="useQuickPrompt('When to repair vs replace appliances')">Repair vs Replace</button>
                                    <button class="quick-prompt" onclick="useQuickPrompt('Cold room maintenance for businesses')">Cold Room Care</button>
                                    <button class="quick-prompt" onclick="useQuickPrompt('Energy efficient appliances guide')">Energy Efficiency</button>
                                </div>
                                
                                <div class="input-group">
                                    <input type="text" class="form-control" id="messageInput" 
                                           placeholder="Describe the content you want to generate..." 
                                           onkeypress="handleKeyPress(event)">
                                    <button class="btn btn-primary" onclick="sendMessage()" id="sendBtn">
                                        <i class="fas fa-paper-plane"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-lightbulb me-2"></i>Tips for Better Results</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Be specific about the topic</li>
                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Mention target audience if relevant</li>
                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Include keywords you want to target</li>
                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Specify the tone (professional, friendly, etc.)</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-history me-2"></i>Recent Generations</h6>
                            </div>
                            <div class="card-body">
                                <div id="recentGenerations">
                                    <p class="text-muted">No recent generations</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let chatHistory = [];
        
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        function useQuickPrompt(prompt) {
            document.getElementById('messageInput').value = prompt;
            sendMessage();
        }
        
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            const contentType = document.getElementById('contentType').value;
            
            if (!message) return;
            
            // Add user message to chat
            addMessage(message, 'user');
            
            // Clear input and show typing indicator
            input.value = '';
            showTypingIndicator();
            
            // Disable send button
            const sendBtn = document.getElementById('sendBtn');
            sendBtn.disabled = true;
            sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            
            // Send to AI API
            fetch('api/ai_content.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    prompt: message,
                    type: contentType
                })
            })
            .then(response => response.json())
            .then(data => {
                hideTypingIndicator();
                
                if (data.success) {
                    addMessage(data.content, 'ai', contentType);
                    addToRecentGenerations(message, data.content, contentType);
                } else {
                    addMessage('Sorry, I encountered an error: ' + (data.error || 'Unknown error'), 'ai', 'error');
                }
            })
            .catch(error => {
                hideTypingIndicator();
                addMessage('Sorry, I encountered a network error. Please try again.', 'ai', 'error');
                console.error('Error:', error);
            })
            .finally(() => {
                // Re-enable send button
                sendBtn.disabled = false;
                sendBtn.innerHTML = '<i class="fas fa-paper-plane"></i>';
            });
        }
        
        function addMessage(content, sender, type = null) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.innerHTML = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';
            
            const bubble = document.createElement('div');
            bubble.className = 'message-bubble';
            
            if (sender === 'ai' && type && type !== 'error') {
                // Create a container with copy button for AI responses
                const container = document.createElement('div');
                container.style.position = 'relative';
                
                const copyBtn = document.createElement('button');
                copyBtn.className = 'btn btn-sm btn-outline-secondary copy-btn';
                copyBtn.innerHTML = '<i class="fas fa-copy"></i>';
                copyBtn.onclick = () => copyToClipboard(content, copyBtn);
                
                if (type === 'content') {
                    bubble.innerHTML = content;
                } else {
                    bubble.textContent = content;
                }
                
                container.appendChild(bubble);
                container.appendChild(copyBtn);
                messageDiv.appendChild(avatar);
                messageDiv.appendChild(container);
            } else {
                if (type === 'error') {
                    bubble.innerHTML = `<span class="text-danger">${content}</span>`;
                } else {
                    bubble.textContent = content;
                }
                messageDiv.appendChild(avatar);
                messageDiv.appendChild(bubble);
            }
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            
            // Store in chat history
            chatHistory.push({
                content: content,
                sender: sender,
                type: type,
                timestamp: new Date()
            });
        }
        
        function showTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'block';
            const messagesContainer = document.getElementById('chatMessages');
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        function hideTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'none';
        }
        
        function copyToClipboard(text, button) {
            navigator.clipboard.writeText(text).then(() => {
                const originalHTML = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check text-success"></i>';
                setTimeout(() => {
                    button.innerHTML = originalHTML;
                }, 2000);
            });
        }
        
        function addToRecentGenerations(prompt, content, type) {
            const container = document.getElementById('recentGenerations');
            
            // Remove "No recent generations" message
            if (container.querySelector('.text-muted')) {
                container.innerHTML = '';
            }
            
            const item = document.createElement('div');
            item.className = 'border-bottom pb-2 mb-2';
            item.innerHTML = `
                <small class="text-muted">${type.toUpperCase()} - ${new Date().toLocaleTimeString()}</small>
                <p class="mb-1 small">${prompt.substring(0, 50)}${prompt.length > 50 ? '...' : ''}</p>
                <button class="btn btn-xs btn-outline-primary" onclick="copyToClipboard('${content.replace(/'/g, "\\'")}', this)">
                    <i class="fas fa-copy"></i> Copy
                </button>
            `;
            
            container.insertBefore(item, container.firstChild);
            
            // Keep only last 5 items
            while (container.children.length > 5) {
                container.removeChild(container.lastChild);
            }
        }
        
        function clearChat() {
            if (confirm('Are you sure you want to clear the chat history?')) {
                const messagesContainer = document.getElementById('chatMessages');
                messagesContainer.innerHTML = `
                    <div class="message ai">
                        <div class="message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="message-bubble">
                            <strong>Chat cleared!</strong><br>
                            How can I help you create content for Lisbon Appliances today?
                        </div>
                    </div>
                `;
                chatHistory = [];
            }
        }
    </script>
</body>
</html>
