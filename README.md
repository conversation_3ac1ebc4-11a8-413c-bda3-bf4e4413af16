# Lisbon Appliances Blog Management System

A complete, modern blog management system with AI-powered content generation, built for the Lisbon Appliances website.

## 🚀 Features

### ✅ **Frontend Features**
- **Dynamic Blog System**: Converted from static HTML to dynamic PHP with database integration
- **Responsive Design**: Mobile-friendly blog listing and article pages
- **SEO Optimized**: Clean URLs, meta tags, and structured data
- **Newsletter Subscription**: Functional newsletter signup with email validation
- **Search & Filtering**: Category-based filtering and search functionality
- **Pagination**: Efficient content pagination for better performance

### ✅ **Admin Dashboard**
- **Complete CRUD Operations**: Create, read, update, delete blog posts
- **CKEditor Integration**: Professional rich text editor with advanced formatting
- **Category Management**: Organize content with categories
- **Tag Management**: Tag system for better content organization
- **User Authentication**: Secure admin access with session management
- **Media Management**: File upload system with image resizing and validation

### ✅ **AI-Powered Content Generation**
- **Google Gemini Integration**: AI-powered content generation
- **Multiple Content Types**: 
  - Full articles
  - Titles and excerpts
  - Article outlines
  - Introductions and conclusions
  - Transcript-to-article conversion
  - Article repurposing
- **Configurable Prompts**: Editable AI prompts for different content types
- **CKEditor Integration**: AI content directly inserted into the editor

### ✅ **Advanced SEO Tools**
- **Meta Management**: Keywords and descriptions for each post
- **Automatic Sitemap**: XML sitemap generation for search engines
- **SEO Analysis**: Dashboard showing SEO issues and optimization opportunities
- **Character Limits**: Built-in validation for optimal SEO lengths

### ✅ **Newsletter System**
- **Subscriber Management**: Complete newsletter subscriber database
- **Export Functionality**: CSV export of subscriber data
- **Status Management**: Active/unsubscribed status tracking
- **API Integration**: RESTful API for newsletter subscriptions

## 📋 Installation

### Prerequisites
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- Google Gemini API key (for AI features)

### Quick Setup

1. **Clone/Download** the project files to your web server
2. **Configure Database**: Update `includes/db_connect.php` with your database credentials
3. **Set Environment Variables**: Update `.env` file with your settings:
   ```
   DB_HOST=localhost
   DB_USERNAME=your_username
   DB_PASSWORD=your_password
   DB_NAME=lisbon_appliances
   GEMINI_API_KEY=your_gemini_api_key
   SITE_URL=http://your-domain.com
   ```
4. **Run Setup**: Visit `http://your-domain.com/setup.php` in your browser
5. **Login**: Access admin at `http://your-domain.com/admin/` with:
   - Username: `admin`
   - Password: `admin123` (change immediately!)

## 🗂️ File Structure

```
├── admin/                  # Admin dashboard
│   ├── add_post.php       # Create new posts
│   ├── edit_post.php      # Edit existing posts
│   ├── posts.php          # Manage all posts
│   ├── categories.php     # Category management
│   ├── tags.php           # Tag management
│   ├── ai_assistant.php   # AI content generator
│   ├── ai_settings.php    # AI prompt configuration
│   ├── seo_tools.php      # SEO analysis tools
│   ├── newsletter.php     # Newsletter management
│   └── api/               # Admin API endpoints
├── api/                   # Public API endpoints
│   └── newsletter_subscribe.php
├── includes/              # Core system files
│   ├── db_connect.php     # Database connection
│   ├── setup_database.php # Database initialization
│   └── env_loader.php     # Environment configuration
├── uploads/               # File upload directory
├── index.php              # Homepage (dynamic)
├── blog.php               # Blog listing page
├── article.php            # Individual article page
├── sitemap.php            # XML sitemap generator
└── setup.php              # Installation script
```

## 🎯 Usage Guide

### Creating Content
1. **Login** to admin dashboard
2. **Add New Post** with title, content, and SEO settings
3. **Use AI Assistant** for content generation
4. **Assign Categories/Tags** for organization
5. **Upload Featured Image** or use URL
6. **Publish** when ready

### AI Content Generation
1. **Access AI Assistant** from admin menu or post editor
2. **Select Content Type** (article, title, excerpt, etc.)
3. **Enter Prompt** describing what you want
4. **Generate Content** and review
5. **Insert into Editor** or copy to clipboard

### SEO Optimization
1. **Visit SEO Tools** in admin dashboard
2. **Review SEO Issues** for existing posts
3. **Generate Sitemap** for search engines
4. **Optimize Meta Data** for better rankings

### Newsletter Management
1. **View Subscribers** in newsletter section
2. **Export Data** as CSV for external tools
3. **Manage Status** (active/unsubscribed)
4. **Monitor Growth** with statistics

## 🔧 Configuration

### AI Settings
- Configure custom prompts in **AI Settings**
- Each content type has its own prompt template
- Use `{prompt}` placeholder for user input
- Enable/disable specific prompt types

### Environment Variables
- `GEMINI_API_KEY`: Your Google Gemini API key
- `SITE_URL`: Your website URL for sitemap generation
- `UPLOAD_PATH`: Directory for file uploads
- `MAX_FILE_SIZE`: Maximum upload file size

## 🛡️ Security Features

- **SQL Injection Protection**: Prepared statements and input sanitization
- **File Upload Validation**: Type and size restrictions
- **Session Management**: Secure admin authentication
- **CSRF Protection**: Form token validation
- **Input Sanitization**: All user inputs properly escaped

## 📱 Mobile Responsive

The entire system is fully responsive and works seamlessly on:
- Desktop computers
- Tablets
- Mobile phones
- All modern browsers

## 🔄 API Endpoints

### Newsletter API
- `POST /api/newsletter_subscribe.php` - Subscribe to newsletter
- Accepts JSON: `{"email": "<EMAIL>", "name": "Optional Name"}`

### Admin APIs
- `POST /admin/api/ai_content.php` - Generate AI content
- `POST /admin/api/upload_image.php` - Upload images

## 🎨 Customization

### Styling
- Main styles in existing CSS files
- Admin styles in `admin/assets/css/admin.css`
- Bootstrap 5 framework for responsive design

### AI Prompts
- Fully customizable through admin interface
- Support for multiple content types
- Template system with placeholders

## 📊 Analytics & SEO

- **XML Sitemap**: Automatically generated and updated
- **Meta Tags**: Comprehensive meta tag management
- **Open Graph**: Social media sharing optimization
- **Schema Markup**: Structured data for search engines

## 🚀 Performance

- **Optimized Queries**: Efficient database operations
- **Image Optimization**: Automatic image resizing
- **Caching Ready**: Structure supports caching implementation
- **Pagination**: Efficient content loading

## 📞 Support

For technical support or customization requests, contact the development team.

---

**Built with ❤️ for Lisbon Appliances**
